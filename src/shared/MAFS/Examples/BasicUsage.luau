--[[
    MAFS Basic Usage Examples
    
    This module demonstrates how to use the MAFS (Modular Audio FootStep System) API
    for common scenarios and integrations.
    
    USAGE:
    - Copy these examples to your own scripts
    - Modify as needed for your specific use case
    - Run from server scripts for material configuration
    - Run from client scripts for volume/enable control
    
    *Dynamic Innovative Studio*
]]

-- Import the MAFS API
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local MAFSAPI = require(ReplicatedStorage.MAFS.Shared.API)

local Examples = {}

--[[
    EXAMPLE 1: Basic Material Configuration
    
    Configure custom materials for different areas of your game
]]
function Examples.ConfigureBasicMaterials()
  print("=== MAFS Example 1: Basic Material Configuration ===")

  -- Configure a snow area
  local snowArea = workspace:FindFirstChild("SnowArea")
  if snowArea then
    local configuredParts = MAFSAPI.SetModelMaterial(snowArea, "Snow")
    print(string.format("Configured %d parts in SnowArea with Snow material", configuredParts))
  end

  -- Configure individual parts
  local metalGrate = workspace:FindFirstChild("MetalGrate")
  if metalGrate then
    local success = MAFSAPI.SetPartMaterial(metalGrate, "MetalGrate")
    if success then
      print("Configured MetalGrate part with MetalGrate material")
    end
  end

  -- Configure parts by name pattern
  local mudParts = MAFSAPI.SetPartsByPattern(workspace, "^Mud_", "Mud")
  print(string.format("Configured %d parts matching 'Mud_*' pattern with Mud material", mudParts))

  -- Configure parts by Roblox material
  local gravelParts = MAFSAPI.SetPartsByMaterial(workspace, Enum.Material.Rock, "Gravel")
  print(string.format("Configured %d Rock material parts with Gravel material", gravelParts))
end

--[[
    EXAMPLE 2: Client-Side Volume and Control
    
    Demonstrate client-side volume control and enable/disable functionality
]]
function Examples.ClientVolumeControl()
  print("=== MAFS Example 2: Client-Side Volume Control ===")

  -- Set volume to 80%
  local success = MAFSAPI.SetVolume(0.8)
  if success then
    print("Set footstep volume to 80%")
  else
    print("Failed to set volume (not on client or system not available)")
  end

  -- Disable footsteps temporarily (useful for cutscenes)
  success = MAFSAPI.SetEnabled(false)
  if success then
    print("Disabled footsteps")

    -- Wait 3 seconds then re-enable
    wait(3)

    success = MAFSAPI.SetEnabled(true)
    if success then
      print("Re-enabled footsteps")
    end
  end
end

--[[
    EXAMPLE 3: System Information and Debugging
    
    Show how to get system information and enable debugging
]]
function Examples.SystemInformation()
  print("=== MAFS Example 3: System Information ===")

  -- Get available materials
  local materials = MAFSAPI.GetAvailableMaterials()
  print("Available custom materials:")
  for _, material in ipairs(materials) do
    print("  - " .. material)
  end

  -- Check if a material is valid
  local isValid = MAFSAPI.IsValidMaterial("Snow")
  print(string.format("Is 'Snow' a valid material? %s", tostring(isValid)))

  isValid = MAFSAPI.IsValidMaterial("InvalidMaterial")
  print(string.format("Is 'InvalidMaterial' a valid material? %s", tostring(isValid)))

  -- Get system information
  local systemInfo = MAFSAPI.GetSystemInfo()
  print("System Information:")
  print(string.format("  Version: %s", systemInfo.Version))
  print(string.format("  Debug Mode: %s", tostring(systemInfo.DebugMode)))
  print(string.format("  Broadcast Radius: %d studs", systemInfo.Settings.BroadcastRadius))
  print(string.format("  Server Cooldown: %.2f seconds", systemInfo.Settings.ServerCooldown))

  -- Enable debug mode
  MAFSAPI.SetDebugMode(true)
  print("Enabled debug mode - you should see debug messages in console")

  -- Disable debug mode after 10 seconds
  spawn(function()
    wait(10)
    MAFSAPI.SetDebugMode(false)
    print("Disabled debug mode")
  end)
end

--[[
    EXAMPLE 4: Advanced Material Configuration
    
    Show advanced material configuration scenarios
]]
function Examples.AdvancedMaterialConfiguration()
  print("=== MAFS Example 4: Advanced Material Configuration ===")

  -- Configure a complex environment
  local dungeon = workspace:FindFirstChild("Dungeon")
  if dungeon then
    -- Configure stone floors
    local stoneParts = MAFSAPI.SetPartsByPattern(dungeon, "Floor_Stone", "Stone")
    print(string.format("Configured %d stone floor parts", stoneParts))

    -- Configure metal grates
    local grateParts = MAFSAPI.SetPartsByPattern(dungeon, "Grate_", "MetalGrate")
    print(string.format("Configured %d metal grate parts", grateParts))

    -- Configure water areas
    local waterParts = MAFSAPI.SetPartsByMaterial(dungeon, Enum.Material.Water, "Water")
    print(string.format("Configured %d water parts", waterParts))
  end

  -- Check current material configuration
  local somePart = workspace:FindFirstChild("TestPart")
  if somePart then
    local currentMaterial = MAFSAPI.GetPartMaterial(somePart)
    if currentMaterial then
      print(string.format("TestPart has custom material: %s", currentMaterial))
    else
      print("TestPart has no custom material (will use Roblox material)")
    end

    -- Configure it with a custom material
    MAFSAPI.SetPartMaterial(somePart, "Snow")
    print("Configured TestPart with Snow material")

    -- Clear the custom material
    MAFSAPI.ClearPartMaterial(somePart)
    print("Cleared custom material from TestPart")
  end
end

--[[
    EXAMPLE 5: Gun System Integration
    
    Example of how to integrate MAFS with a gun system
]]
function Examples.GunSystemIntegration()
  print("=== MAFS Example 5: Gun System Integration ===")

  -- This would typically be called when your gun system starts up
  local function setupGunSystemFootsteps()
    -- Configure special materials for gun system environments
    local shootingRange = workspace:FindFirstChild("ShootingRange")
    if shootingRange then
      -- Configure concrete shooting lanes
      MAFSAPI.SetPartsByPattern(shootingRange, "Lane_", "Concrete")

      -- Configure metal target areas
      MAFSAPI.SetPartsByPattern(shootingRange, "Target_", "Metal")

      print("Gun system: Configured shooting range materials")
    end

    -- Set appropriate volume for tactical gameplay
    MAFSAPI.SetVolume(0.6) -- Slightly quieter for better audio balance

    print("Gun system: MAFS integration complete")
  end

  -- This would typically be called during intense combat
  local function muteFootstepsForCombat()
    MAFSAPI.SetEnabled(false)
    print("Gun system: Muted footsteps for combat sequence")

    -- Re-enable after combat (this would be triggered by your combat system)
    spawn(function()
      wait(30) -- Example: 30 second combat sequence
      MAFSAPI.SetEnabled(true)
      print("Gun system: Re-enabled footsteps after combat")
    end)
  end

  -- Example usage
  setupGunSystemFootsteps()

  -- Simulate combat after 5 seconds
  spawn(function()
    wait(5)
    muteFootstepsForCombat()
  end)
end

--[[
    EXAMPLE 6: Performance Monitoring
    
    Show how to monitor MAFS performance
]]
function Examples.PerformanceMonitoring()
  print("=== MAFS Example 6: Performance Monitoring ===")

  -- Get performance metrics
  local metrics = MAFSAPI.GetPerformanceMetrics()
  if metrics then
    print("Performance Metrics:")
    print(string.format("  Active Sounds: %d", metrics.activeSounds))
    print(string.format("  Total Sounds Created: %d", metrics.totalSoundsCreated))
    print(string.format("  Pool Hits: %d", metrics.poolHits))

    -- Calculate efficiency
    if metrics.totalSoundsCreated > 0 then
      local efficiency = (metrics.poolHits / metrics.totalSoundsCreated) * 100
      print(string.format("  Pool Efficiency: %.1f%%", efficiency))
    end
  else
    print("Performance metrics not available")
  end
end

--[[
    Run all examples
]]
function Examples.RunAllExamples()
  print("=== Running All MAFS Examples ===")

  Examples.ConfigureBasicMaterials()
  wait(1)

  Examples.ClientVolumeControl()
  wait(1)

  Examples.SystemInformation()
  wait(1)

  Examples.AdvancedMaterialConfiguration()
  wait(1)

  Examples.GunSystemIntegration()
  wait(1)

  Examples.PerformanceMonitoring()

  print("=== All MAFS Examples Complete ===")
end

return Examples

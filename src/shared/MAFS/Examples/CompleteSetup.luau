--[[
    MAFS Complete Setup Example
    
    This script demonstrates a complete MAFS setup for a game environment.
    It shows how to configure materials, test the system, and monitor performance.
    
    USAGE:
    - Run this script from a server script to set up a complete MAFS environment
    - Modify the configurations to match your game's needs
    - Use as a reference for implementing MAFS in your own game
    
    *Dynamic Innovative Studio*
]]

-- Import MAFS modules
local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")

local MAFSAPI = require(ReplicatedStorage.MAFS.Shared.API)
local MAFSTests = require(ReplicatedStorage.MAFS.Tests.MAFSTests)

local CompleteSetup = {}

--[[
    Creates a test environment with various materials
]]
function CompleteSetup.CreateTestEnvironment()
  print("=== Creating MAFS Test Environment ===")

  -- Create a test area
  local testArea = Instance.new("Model")
  testArea.Name = "MAFS_TestArea"
  testArea.Parent = workspace

  -- Create different material zones
  local zones = {
    {
      name = "SnowZone",
      material = Enum.Material.Ice,
      customMaterial = "Snow",
      position = Vector3.new(0, 5, 0),
    },
    {
      name = "GravelZone",
      material = Enum.Material.Rock,
      customMaterial = "Gravel",
      position = Vector3.new(20, 5, 0),
    },
    {
      name = "MudZone",
      material = Enum.Material.Ground,
      customMaterial = "Mud",
      position = Vector3.new(40, 5, 0),
    },
    {
      name = "MetalGrateZone",
      material = Enum.Material.Metal,
      customMaterial = "MetalGrate",
      position = Vector3.new(60, 5, 0),
    },
    {
      name = "WoodZone",
      material = Enum.Material.Wood,
      customMaterial = nil,
      position = Vector3.new(80, 5, 0),
    },
    {
      name = "ConcreteZone",
      material = Enum.Material.Concrete,
      customMaterial = nil,
      position = Vector3.new(100, 5, 0),
    },
  }

  for _, zone in ipairs(zones) do
    -- Create the floor
    local floor = Instance.new("Part")
    floor.Name = zone.name .. "_Floor"
    floor.Material = zone.material
    floor.Size = Vector3.new(15, 1, 15)
    floor.Position = zone.position
    floor.Anchored = true
    floor.BrickColor = BrickColor.new("Medium stone grey")
    floor.Parent = testArea

    -- Configure custom material if specified
    if zone.customMaterial then
      local success = MAFSAPI.SetPartMaterial(floor, zone.customMaterial)
      if success then
        print(string.format("Configured %s with %s material", floor.Name, zone.customMaterial))
      end
    end

    -- Create a sign
    local sign = Instance.new("Part")
    sign.Name = zone.name .. "_Sign"
    sign.Material = Enum.Material.Plastic
    sign.Size = Vector3.new(1, 3, 0.2)
    sign.Position = zone.position + Vector3.new(0, 2, 8)
    sign.Anchored = true
    sign.BrickColor = BrickColor.new("Bright blue")
    sign.Parent = testArea

    local gui = Instance.new("SurfaceGui")
    gui.Face = Enum.NormalId.Front
    gui.Parent = sign

    local label = Instance.new("TextLabel")
    label.Size = UDim2.new(1, 0, 1, 0)
    label.BackgroundTransparency = 1
    label.Text = zone.customMaterial or tostring(zone.material):gsub("Enum.Material.", "")
    label.TextColor3 = Color3.new(1, 1, 1)
    label.TextScaled = true
    label.Font = Enum.Font.SourceSansBold
    label.Parent = gui
  end

  print("Test environment created successfully!")
  return testArea
end

--[[
    Configures a complete game environment
]]
function CompleteSetup.ConfigureGameEnvironment()
  print("=== Configuring Game Environment ===")

  -- Configure spawn area
  local spawnArea = workspace:FindFirstChild("SpawnLocation")
  if spawnArea then
    MAFSAPI.SetPartMaterial(spawnArea, "Concrete")
    print("Configured spawn area with Concrete material")
  end

  -- Configure buildings by pattern
  local buildingParts = MAFSAPI.SetPartsByPattern(workspace, "Building_", "Concrete")
  print(string.format("Configured %d building parts with Concrete material", buildingParts))

  -- Configure outdoor areas
  local grassParts = MAFSAPI.SetPartsByMaterial(workspace, Enum.Material.Grass, "Grass")
  print(string.format("Configured %d grass parts", grassParts))

  local sandParts = MAFSAPI.SetPartsByMaterial(workspace, Enum.Material.Sand, "Sand")
  print(string.format("Configured %d sand parts", sandParts))

  -- Configure special areas
  local metalParts = MAFSAPI.SetPartsByPattern(workspace, "Metal_", "MetalGrate")
  print(string.format("Configured %d metal parts with MetalGrate material", metalParts))

  local mudParts = MAFSAPI.SetPartsByPattern(workspace, "Swamp_", "Mud")
  print(string.format("Configured %d swamp parts with Mud material", mudParts))

  print("Game environment configured successfully!")
end

--[[
    Sets up performance monitoring
]]
function CompleteSetup.SetupPerformanceMonitoring()
  print("=== Setting Up Performance Monitoring ===")

  -- Enable debug mode for detailed logging
  MAFSAPI.SetDebugMode(true)
  print("Debug mode enabled")

  -- Set up periodic performance reports
  spawn(function()
    while true do
      wait(30) -- Report every 30 seconds

      local metrics = MAFSAPI.GetPerformanceMetrics()
      if metrics then
        print("\n=== MAFS Performance Report ===")
        print(string.format("Active Sounds: %d", metrics.sounds.currentActive))
        print(string.format("Pool Efficiency: %.1f%%", metrics.sounds.poolEfficiency * 100))
        print(string.format("Requests/Second: %.1f", metrics.network.requestsPerSecond))
        print(
          string.format(
            "Validation Failure Rate: %.1f%%",
            metrics.network.validationFailureRate * 100
          )
        )
        print(
          string.format("Memory Usage: %.1f MB", metrics.performance.memoryUsage / (1024 * 1024))
        )
        print("===============================\n")
      end
    end
  end)

  print("Performance monitoring set up successfully!")
end

--[[
    Demonstrates client-side features (run on client)
]]
function CompleteSetup.DemonstrateClientFeatures()
  if not RunService:IsClient() then
    print("Client features can only be demonstrated on the client")
    return
  end

  print("=== Demonstrating Client Features ===")

  -- Set initial volume
  MAFSAPI.SetVolume(0.8)
  print("Set volume to 80%")

  -- Demonstrate volume control
  spawn(function()
    wait(5)
    print("Reducing volume to 50%...")
    MAFSAPI.SetVolume(0.5)

    wait(5)
    print("Restoring volume to 80%...")
    MAFSAPI.SetVolume(0.8)
  end)

  -- Demonstrate enable/disable
  spawn(function()
    wait(15)
    print("Disabling footsteps for 5 seconds...")
    MAFSAPI.SetEnabled(false)

    wait(5)
    print("Re-enabling footsteps...")
    MAFSAPI.SetEnabled(true)
  end)

  print("Client feature demonstration started!")
end

--[[
    Runs comprehensive tests
]]
function CompleteSetup.RunTests()
  print("=== Running MAFS Tests ===")

  local success = MAFSTests.RunAllTests()

  if success then
    print("🎉 All tests passed! MAFS is working correctly.")
  else
    print("❌ Some tests failed. Please check the test output above.")
  end

  return success
end

--[[
    Displays system information
]]
function CompleteSetup.DisplaySystemInfo()
  print("=== MAFS System Information ===")

  local systemInfo = MAFSAPI.GetSystemInfo()

  print(string.format("Version: %s", systemInfo.Version))
  print(string.format("Debug Mode: %s", tostring(systemInfo.DebugMode)))
  print("")

  print("Available Materials:")
  for _, material in ipairs(systemInfo.AvailableMaterials) do
    print(string.format("  - %s", material))
  end
  print("")

  print("System Settings:")
  print(string.format("  Broadcast Radius: %d studs", systemInfo.Settings.BroadcastRadius))
  print(string.format("  Server Cooldown: %.2f seconds", systemInfo.Settings.ServerCooldown))
  print(string.format("  Client Cooldown: %.2f seconds", systemInfo.Settings.ClientCooldown))
  print(string.format("  Movement Threshold: %.2f", systemInfo.Settings.MovementThreshold))
  print("")

  local metrics = MAFSAPI.GetPerformanceMetrics()
  if metrics then
    print("Current Performance:")
    print(string.format("  Uptime: %.1f seconds", metrics.performance.uptime))
    print(string.format("  Active Sounds: %d", metrics.sounds.currentActive))
    print(string.format("  Total Requests: %d", metrics.network.footstepRequests))
    print(string.format("  Pool Efficiency: %.1f%%", metrics.sounds.poolEfficiency * 100))
  end

  print("================================")
end

--[[
    Complete setup function - runs everything
]]
function CompleteSetup.RunCompleteSetup()
  print("🚀 Starting MAFS Complete Setup...")
  print("")

  -- Display system information
  CompleteSetup.DisplaySystemInfo()
  print("")

  -- Run tests first to ensure everything is working
  local testsPass = CompleteSetup.RunTests()
  print("")

  if not testsPass then
    warn("⚠️ Tests failed! Setup will continue but there may be issues.")
    print("")
  end

  -- Create test environment
  CompleteSetup.CreateTestEnvironment()
  print("")

  -- Configure game environment
  CompleteSetup.ConfigureGameEnvironment()
  print("")

  -- Set up performance monitoring
  CompleteSetup.SetupPerformanceMonitoring()
  print("")

  -- Demonstrate client features if on client
  CompleteSetup.DemonstrateClientFeatures()
  print("")

  print("✅ MAFS Complete Setup finished!")
  print("")
  print("🎮 Walk around the test environment to hear different footstep sounds!")
  print("📊 Performance reports will be printed every 30 seconds.")
  print("🔧 Use MAFSAPI functions to further customize the system.")
  print("")
  print("For more information, see the MAFS documentation and examples.")
end

--[[
    Quick setup for production use
]]
function CompleteSetup.QuickProductionSetup()
  print("🚀 MAFS Quick Production Setup...")

  -- Disable debug mode for production
  MAFSAPI.SetDebugMode(false)

  -- Configure common game areas
  CompleteSetup.ConfigureGameEnvironment()

  -- Set up basic performance monitoring (less verbose)
  spawn(function()
    while true do
      wait(300) -- Report every 5 minutes

      local metrics = MAFSAPI.GetPerformanceMetrics()
      if metrics and metrics.sounds.currentActive > 20 then
        print(
          string.format("MAFS: High sound usage - %d active sounds", metrics.sounds.currentActive)
        )
      end
    end
  end)

  print("✅ MAFS Production Setup complete!")
end

return CompleteSetup

--[[
    MAFS API Module
    
    Public API for the Modular Audio FootStep System (MAFS)
    
    ARCHITECTURE ROLE:
    - Provides clean public interface for external systems
    - Abstracts internal complexity from consumers
    - Enables integration with gun systems and other frameworks
    
    USAGE:
    - Import this module to interact with MAFS from external systems
    - Suitable for gun engines, character systems, and other frameworks
    
    *Dynamic Innovative Studio*
]]

local MAFSAPI = {}

-- Services
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")

-- Import MAFS modules
local MAFSConfig = require(ReplicatedStorage.Configurations.Systems.MAFS)
local MAFSUtils = require(ReplicatedStorage.MAFS.Shared.Utils)
local MaterialConfig = require(ReplicatedStorage.MAFS.Shared.MaterialConfig)
local PerformanceMonitor = require(ReplicatedStorage.MAFS.Shared.PerformanceMonitor)

-- Client-only modules (loaded conditionally)
local FootstepClient = nil
if RunService:IsClient() then
  local success, module = pcall(function()
    return require(script.Parent.Parent.Parent.client.MAFS.FootStep_Client)
  end)
  if success then
    FootstepClient = module
  end
end

--[[
    PUBLIC API - CONFIGURATION
]]

--[[
    Sets a custom footstep material for a single part
    
    @param part (BasePart) - The part to configure
    @param materialName (string) - The custom material name ("Snow", "Gravel", etc.)
    @return (boolean) - Success status
    
    Example:
        MAFSAPI.SetPartMaterial(workspace.SnowPile, "Snow")
]]
function MAFSAPI.SetPartMaterial(part, materialName)
  return MaterialConfig.ConfigurePart(part, materialName)
end

--[[
    Sets a custom footstep material for all parts in a model
    
    @param model (Model) - The model containing parts to configure
    @param materialName (string) - The custom material name
    @return (number) - Number of parts configured
    
    Example:
        MAFSAPI.SetModelMaterial(workspace.WoodenBridge, "Wood")
]]
function MAFSAPI.SetModelMaterial(model, materialName)
  return MaterialConfig.ConfigureModel(model, materialName)
end

--[[
    Sets custom footstep materials for parts matching a name pattern
    
    @param parent (Instance) - The parent to search
    @param namePattern (string) - Lua pattern to match part names
    @param materialName (string) - The custom material name
    @return (number) - Number of parts configured
    
    Example:
        MAFSAPI.SetPartsByPattern(workspace, "^Metal_", "MetalGrate")
]]
function MAFSAPI.SetPartsByPattern(parent, namePattern, materialName)
  return MaterialConfig.ConfigureByNamePattern(parent, namePattern, materialName)
end

--[[
    Sets custom footstep materials for parts with specific Roblox material
    
    @param parent (Instance) - The parent to search
    @param robloxMaterial (Enum.Material) - The Roblox material to match
    @param materialName (string) - The custom material name
    @return (number) - Number of parts configured
    
    Example:
        MAFSAPI.SetPartsByMaterial(workspace, Enum.Material.Sand, "DeepSand")
]]
function MAFSAPI.SetPartsByMaterial(parent, robloxMaterial, materialName)
  return MaterialConfig.ConfigureByRobloxMaterial(parent, robloxMaterial, materialName)
end

--[[
    PUBLIC API - SYSTEM CONTROL (CLIENT ONLY)
]]

--[[
    Sets the volume for all footstep sounds (Client only)
    
    @param volume (number) - Volume level (0-1)
    @return (boolean) - Success status
    
    Example:
        MAFSAPI.SetVolume(0.8)
]]
function MAFSAPI.SetVolume(volume)
  if not RunService:IsClient() then
    warn("MAFS: SetVolume can only be called on the client")
    return false
  end

  if not FootstepClient then
    warn("MAFS: FootstepClient not available")
    return false
  end

  FootstepClient.SetVolume(volume)
  return true
end

--[[
    Enables or disables footstep sound playback (Client only)
    
    @param enabled (boolean) - Whether footsteps should be played
    @return (boolean) - Success status
    
    Example:
        MAFSAPI.SetEnabled(false) -- Disable for cutscenes
        MAFSAPI.SetEnabled(true)  -- Re-enable
]]
function MAFSAPI.SetEnabled(enabled)
  if not RunService:IsClient() then
    warn("MAFS: SetEnabled can only be called on the client")
    return false
  end

  if not FootstepClient then
    warn("MAFS: FootstepClient not available")
    return false
  end

  FootstepClient.SetEnabled(enabled)
  return true
end

--[[
    PUBLIC API - SYSTEM INFORMATION
]]

--[[
    Gets the list of available custom materials
    
    @return (table) - Array of custom material names
    
    Example:
        local materials = MAFSAPI.GetAvailableMaterials()
        for _, material in ipairs(materials) do
            print("Available material:", material)
        end
]]
function MAFSAPI.GetAvailableMaterials()
  local materials = {}
  for materialName, _ in pairs(MAFSConfig.CustomMaterials) do
    table.insert(materials, materialName)
  end
  return materials
end

--[[
    Checks if a material name is valid
    
    @param materialName (string) - The material name to validate
    @return (boolean) - Whether the material is valid
    
    Example:
        if MAFSAPI.IsValidMaterial("Snow") then
            print("Snow is a valid material")
        end
]]
function MAFSAPI.IsValidMaterial(materialName)
  return MAFSUtils.ValidateMaterialName(materialName)
end

--[[
    Gets the current material configuration for a part
    
    @param part (BasePart) - The part to check
    @return (string|nil) - The custom material name, or nil if not configured
    
    Example:
        local material = MAFSAPI.GetPartMaterial(workspace.SomePart)
        if material then
            print("Part has custom material:", material)
        end
]]
function MAFSAPI.GetPartMaterial(part)
  return MaterialConfig.GetPartMaterial(part)
end

--[[
    PUBLIC API - DEBUG AND MONITORING
]]

--[[
    Enables or disables debug mode
    
    @param enabled (boolean) - Whether debug mode should be enabled
    
    Example:
        MAFSAPI.SetDebugMode(true) -- Enable debug logging
]]
function MAFSAPI.SetDebugMode(enabled)
  MAFSConfig.SetDebugMode(enabled)
end

--[[
    Gets the current debug mode status
    
    @return (boolean) - Whether debug mode is enabled
]]
function MAFSAPI.IsDebugMode()
  return MAFSConfig.IsDebugMode()
end

--[[
    Gets performance metrics (if available)
    
    @return (table|nil) - Performance metrics or nil if not available
    
    Example:
        local metrics = MAFSAPI.GetPerformanceMetrics()
        if metrics then
            print("Active sounds:", metrics.activeSounds)
        end
]]
function MAFSAPI.GetPerformanceMetrics()
  return PerformanceMonitor.GetMetrics()
end

--[[
    PUBLIC API - UTILITY FUNCTIONS
]]

--[[
    Clears custom material configuration from a part
    
    @param part (BasePart) - The part to clear
    @return (boolean) - Success status
    
    Example:
        MAFSAPI.ClearPartMaterial(workspace.SomePart)
]]
function MAFSAPI.ClearPartMaterial(part)
  return MaterialConfig.ClearPartMaterial(part)
end

--[[
    Gets system configuration information
    
    @return (table) - System configuration data
]]
function MAFSAPI.GetSystemInfo()
  return {
    Version = "1.0.0",
    DebugMode = MAFSConfig.IsDebugMode(),
    AvailableMaterials = MAFSAPI.GetAvailableMaterials(),
    Settings = {
      BroadcastRadius = MAFSConfig.Settings.BroadcastRadius,
      ServerCooldown = MAFSConfig.Settings.ServerCooldown,
      ClientCooldown = MAFSConfig.Settings.ClientCooldown,
      MovementThreshold = MAFSConfig.Settings.MovementThreshold,
    },
  }
end

return MAFSAPI

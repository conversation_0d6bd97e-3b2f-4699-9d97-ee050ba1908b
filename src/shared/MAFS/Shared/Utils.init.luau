--[[
    Utils.luau

    Utility module for configuring custom footstep materials

    ARCHITECTURE ROLE:
    - Provides the Material Configuration System component
    - Enables custom material assignments for parts and models
    - Supports batch operations for efficient setup

    USAGE:
    - Import this module to configure custom footstep materials
    - Use before game start or when creating new environments

    *Dynamic Innovative Studio*
]]

local Utils = {}

local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- Import MAFS configuration
local MAFSConfig = require(ReplicatedStorage.Configurations.Systems.MAFS)
local PerformanceMonitor = require(ReplicatedStorage.MAFS.Shared.PerformanceMonitor)

-- Sound Pool Management
local MAX_CACHED_SOUNDS = 20
local cachedSounds = {}

-- Performance Metrics
local Stats = {
  activeSounds = 0,
  totalSoundsCreated = 0,
  poolHits = 0,
}

function Utils.GetSound(template)
  if not template or not template:IsA("Sound") then
    warn("MAFS: Invalid sound template provided")
    return nil
  end

  if #cachedSounds > 0 then
    local sound = table.remove(cachedSounds)
    if sound and sound.Parent then
      PerformanceMonitor.TrackPoolHit()
      return sound
    end
  end

  local newSound = template:Clone()
  if newSound then
    Stats.totalSoundsCreated = Stats.totalSoundsCreated + 1
    PerformanceMonitor.TrackPoolMiss()
    return newSound
  end

  return nil
end

function Utils.ReturnSound(sound)
  if not sound or not sound:IsA("Sound") then
    return
  end

  if #cachedSounds < MAX_CACHED_SOUNDS then
    sound.Playing = false
    sound.TimePosition = 0
    table.insert(cachedSounds, sound)
    Stats.poolHits = Stats.poolHits + 1
  else
    sound:Destroy()
  end
end

function Utils.CleanupSoundPool()
  for _, sound in ipairs(cachedSounds) do
    sound:Destroy()
  end
  table.clear(cachedSounds)
end

function Utils.GetPerformanceMetrics()
  return Stats
end

function Utils.TrackSoundCreated()
  Stats.totalSoundsCreated = Stats.totalSoundsCreated + 1
  Stats.activeSounds = Stats.activeSounds + 1
  PerformanceMonitor.TrackSoundCreated()
end

function Utils.TrackSoundDestroyed()
  Stats.activeSounds = math.max(0, Stats.activeSounds - 1)
  PerformanceMonitor.TrackSoundDestroyed()
end

--[[
    Sets a custom footstep material for a single part

    @param part (BasePart) - The part to configure
    @param materialName (string) - The custom material name to assign

    Example:
        local part = workspace.StonePath
        Utils.SetPartMaterial(part, "Stone")
]]
function Utils.SetPartMaterial(part, materialName)
  if not part then
    warn("MAFS: Invalid part provided")
    return
  end
  if not materialName or not Utils.ValidateMaterialName(materialName) then
    warn("MAFS: Invalid material name:", materialName)
    return
  end

  part:SetAttribute("FootstepMaterial", materialName)
end

--[[
    Sets a custom footstep material for all parts in a model

    @param model (Model) - The model containing parts to configure
    @param materialName (string) - The custom material name to assign

    Example:
        local bridge = workspace.WoodenBridge
        Utils.SetModelMaterial(bridge, "WoodPlanks")
]]
function Utils.SetModelMaterial(model, materialName)
  if not model or not materialName then
    return
  end

  for _, part in pairs(model:GetDescendants()) do
    if part:IsA("BasePart") then
      part:SetAttribute("FootstepMaterial", materialName)
    end
  end
end

-- Check if Debug Mode is enabled
function Utils.isDebugMode()
  return MAFSConfig.IsDebugMode()
end

--[[
    Sets a custom footstep material for parts matching a name pattern

    @param parent (Instance) - The parent to search for matching parts
    @param namePattern (string) - Lua pattern to match part names against
    @param materialName (string) - The custom material name to assign

    Example:
        Utils.SetPartsByNamePattern(workspace, "^Snow_", "Snow")
        -- This would match parts named "Snow_Pile", "Snow_Ground", etc.
]]
function Utils.SetPartsByNamePattern(parent, namePattern, materialName)
  if not parent or not namePattern or not materialName then
    return
  end

  for _, part in pairs(parent:GetDescendants()) do
    if part:IsA("BasePart") and string.match(part.Name, namePattern) then
      part:SetAttribute("FootstepMaterial", materialName)
    end
  end
end

--[[
    Sets a custom footstep material for all parts with a specific material type

    @param parent (Instance) - The parent to search for matching parts
    @param material (Enum.Material) - The Roblox material type to match
    @param materialName (string) - The custom material name to assign

    Example:
        Utils.SetPartsByMaterial(workspace, Enum.Material.Sand, "DeepSand")
        -- This would make all sand parts use the "DeepSand" footstep sound
]]
function Utils.SetPartsByMaterial(parent, material, materialName)
  if not parent or not material or not materialName then
    return
  end

  for _, part in pairs(parent:GetDescendants()) do
    if part:IsA("BasePart") and part.Material == material then
      part:SetAttribute("FootstepMaterial", materialName)
    end
  end
end

function Utils.ValidateMaterialName(materialName)
  -- Check if it's a valid custom material
  return MAFSConfig.CustomMaterials[materialName] ~= nil
end

--[[
    USAGE EXAMPLES:

    -- Configure a snow environment
    local snowscape = workspace.Snowscape
    Utils.SetModelMaterial(snowscape, "Snow")

    -- Set all marble materials to use the Marble footstep sound
    Utils.SetPartsByMaterial(workspace, Enum.Material.Marble, "Marble")

    -- Configure a metal grate bridge
    local grate = workspace.MetalGrateBridge
    Utils.SetModelMaterial(grate, "MetalGrate")
]]

return Utils

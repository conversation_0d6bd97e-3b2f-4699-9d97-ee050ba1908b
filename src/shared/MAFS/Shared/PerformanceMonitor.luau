--[[
    MAFS Performance Monitor
    
    Advanced performance monitoring and analytics for the MAFS system
    
    ARCHITECTURE ROLE:
    - Tracks system performance metrics
    - Monitors resource usage
    - Provides optimization insights
    - Enables performance debugging
    
    FEATURES:
    - Real-time performance tracking
    - Memory usage monitoring
    - Network event counting
    - Sound pool efficiency analysis
    - Performance alerts and warnings
    
    *Dynamic Innovative Studio*
]]

local PerformanceMonitor = {}

-- Services
local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")

-- Import MAFS configuration
local MAFSConfig = require(ReplicatedStorage.Configurations.Systems.MAFS)

-- Performance tracking data
local performanceData = {
  -- Sound system metrics
  sounds = {
    totalCreated = 0,
    totalDestroyed = 0,
    currentActive = 0,
    poolHits = 0,
    poolMisses = 0,
    maxConcurrent = 0,
  },

  -- Network metrics
  network = {
    footstepRequests = 0,
    footstepBroadcasts = 0,
    validationFailures = 0,
    bytesTransmitted = 0,
  },

  -- Material system metrics
  materials = {
    resolutionAttempts = 0,
    customMaterialHits = 0,
    robloxMaterialHits = 0,
    defaultMaterialHits = 0,
    attributeLookups = 0,
    raycastOperations = 0,
  },

  -- Performance metrics
  performance = {
    averageFrameTime = 0,
    peakFrameTime = 0,
    memoryUsage = 0,
    startTime = tick(),
    lastUpdateTime = tick(),
  },

  -- Error tracking
  errors = {
    soundPlaybackFailures = 0,
    networkErrors = 0,
    materialResolutionErrors = 0,
    validationErrors = 0,
  },
}

-- Performance thresholds for warnings
local PERFORMANCE_THRESHOLDS = {
  MAX_CONCURRENT_SOUNDS = 50,
  MAX_FRAME_TIME = 0.016, -- 60 FPS target
  MAX_MEMORY_USAGE = 100 * 1024 * 1024, -- 100 MB
  MIN_POOL_EFFICIENCY = 0.7, -- 70%
  MAX_VALIDATION_FAILURE_RATE = 0.1, -- 10%
}

-- Update frequency for performance monitoring
local UPDATE_INTERVAL = 1.0 -- seconds
local lastUpdateTime = 0

--[[
    Sound System Tracking
]]
function PerformanceMonitor.TrackSoundCreated()
  performanceData.sounds.totalCreated = performanceData.sounds.totalCreated + 1
  performanceData.sounds.currentActive = performanceData.sounds.currentActive + 1

  -- Update max concurrent sounds
  if performanceData.sounds.currentActive > performanceData.sounds.maxConcurrent then
    performanceData.sounds.maxConcurrent = performanceData.sounds.currentActive
  end

  -- Check for performance warning
  if performanceData.sounds.currentActive > PERFORMANCE_THRESHOLDS.MAX_CONCURRENT_SOUNDS then
    warn(
      string.format(
        "MAFS Performance Warning: %d concurrent sounds (threshold: %d)",
        performanceData.sounds.currentActive,
        PERFORMANCE_THRESHOLDS.MAX_CONCURRENT_SOUNDS
      )
    )
  end
end

function PerformanceMonitor.TrackSoundDestroyed()
  performanceData.sounds.totalDestroyed = performanceData.sounds.totalDestroyed + 1
  performanceData.sounds.currentActive = math.max(0, performanceData.sounds.currentActive - 1)
end

function PerformanceMonitor.TrackPoolHit()
  performanceData.sounds.poolHits = performanceData.sounds.poolHits + 1
end

function PerformanceMonitor.TrackPoolMiss()
  performanceData.sounds.poolMisses = performanceData.sounds.poolMisses + 1
end

function PerformanceMonitor.TrackSoundPlaybackFailure()
  performanceData.errors.soundPlaybackFailures = performanceData.errors.soundPlaybackFailures + 1
end

--[[
    Network Tracking
]]
function PerformanceMonitor.TrackFootstepRequest()
  performanceData.network.footstepRequests = performanceData.network.footstepRequests + 1
  performanceData.network.bytesTransmitted = performanceData.network.bytesTransmitted + 32 -- Approximate size
end

function PerformanceMonitor.TrackFootstepBroadcast(playerCount)
  performanceData.network.footstepBroadcasts = performanceData.network.footstepBroadcasts + 1
  performanceData.network.bytesTransmitted = performanceData.network.bytesTransmitted
    + (playerCount * 64) -- Approximate size
end

function PerformanceMonitor.TrackValidationFailure()
  performanceData.network.validationFailures = performanceData.network.validationFailures + 1
  performanceData.errors.validationErrors = performanceData.errors.validationErrors + 1
end

function PerformanceMonitor.TrackNetworkError()
  performanceData.errors.networkErrors = performanceData.errors.networkErrors + 1
end

--[[
    Material System Tracking
]]
function PerformanceMonitor.TrackMaterialResolution(resolutionType)
  performanceData.materials.resolutionAttempts = performanceData.materials.resolutionAttempts + 1

  if resolutionType == "custom" then
    performanceData.materials.customMaterialHits = performanceData.materials.customMaterialHits + 1
  elseif resolutionType == "roblox" then
    performanceData.materials.robloxMaterialHits = performanceData.materials.robloxMaterialHits + 1
  elseif resolutionType == "default" then
    performanceData.materials.defaultMaterialHits = performanceData.materials.defaultMaterialHits
      + 1
  end
end

function PerformanceMonitor.TrackAttributeLookup()
  performanceData.materials.attributeLookups = performanceData.materials.attributeLookups + 1
end

function PerformanceMonitor.TrackRaycastOperation()
  performanceData.materials.raycastOperations = performanceData.materials.raycastOperations + 1
end

function PerformanceMonitor.TrackMaterialResolutionError()
  performanceData.errors.materialResolutionErrors = performanceData.errors.materialResolutionErrors
    + 1
end

--[[
    Performance Analysis
]]
function PerformanceMonitor.CalculatePoolEfficiency()
  local totalPoolOperations = performanceData.sounds.poolHits + performanceData.sounds.poolMisses
  if totalPoolOperations == 0 then
    return 1.0 -- Perfect efficiency when no operations
  end
  return performanceData.sounds.poolHits / totalPoolOperations
end

function PerformanceMonitor.CalculateValidationFailureRate()
  local totalRequests = performanceData.network.footstepRequests
  if totalRequests == 0 then
    return 0.0
  end
  return performanceData.network.validationFailures / totalRequests
end

function PerformanceMonitor.CalculateUptime()
  return tick() - performanceData.performance.startTime
end

function PerformanceMonitor.CalculateRequestsPerSecond()
  local uptime = PerformanceMonitor.CalculateUptime()
  if uptime == 0 then
    return 0
  end
  return performanceData.network.footstepRequests / uptime
end

--[[
    Performance Monitoring Update
]]
function PerformanceMonitor.Update()
  local currentTime = tick()

  -- Update frame time tracking
  local deltaTime = currentTime - performanceData.performance.lastUpdateTime
  performanceData.performance.lastUpdateTime = currentTime

  if deltaTime > 0 then
    -- Update average frame time (simple moving average)
    performanceData.performance.averageFrameTime = (
      performanceData.performance.averageFrameTime * 0.9
    ) + (deltaTime * 0.1)

    -- Update peak frame time
    if deltaTime > performanceData.performance.peakFrameTime then
      performanceData.performance.peakFrameTime = deltaTime
    end
  end

  -- Update memory usage (approximate)
  performanceData.performance.memoryUsage = collectgarbage("count") * 1024

  -- Check performance thresholds
  PerformanceMonitor.CheckPerformanceThresholds()
end

function PerformanceMonitor.CheckPerformanceThresholds()
  -- Check pool efficiency
  local poolEfficiency = PerformanceMonitor.CalculatePoolEfficiency()
  if poolEfficiency < PERFORMANCE_THRESHOLDS.MIN_POOL_EFFICIENCY then
    warn(
      string.format(
        "MAFS Performance Warning: Low pool efficiency %.1f%% (threshold: %.1f%%)",
        poolEfficiency * 100,
        PERFORMANCE_THRESHOLDS.MIN_POOL_EFFICIENCY * 100
      )
    )
  end

  -- Check validation failure rate
  local failureRate = PerformanceMonitor.CalculateValidationFailureRate()
  if failureRate > PERFORMANCE_THRESHOLDS.MAX_VALIDATION_FAILURE_RATE then
    warn(
      string.format(
        "MAFS Performance Warning: High validation failure rate %.1f%% (threshold: %.1f%%)",
        failureRate * 100,
        PERFORMANCE_THRESHOLDS.MAX_VALIDATION_FAILURE_RATE * 100
      )
    )
  end

  -- Check frame time
  if performanceData.performance.averageFrameTime > PERFORMANCE_THRESHOLDS.MAX_FRAME_TIME then
    warn(
      string.format(
        "MAFS Performance Warning: High frame time %.3fs (threshold: %.3fs)",
        performanceData.performance.averageFrameTime,
        PERFORMANCE_THRESHOLDS.MAX_FRAME_TIME
      )
    )
  end
end

--[[
    Public API
]]
function PerformanceMonitor.GetMetrics()
  return {
    sounds = {
      totalCreated = performanceData.sounds.totalCreated,
      totalDestroyed = performanceData.sounds.totalDestroyed,
      currentActive = performanceData.sounds.currentActive,
      maxConcurrent = performanceData.sounds.maxConcurrent,
      poolEfficiency = PerformanceMonitor.CalculatePoolEfficiency(),
    },

    network = {
      footstepRequests = performanceData.network.footstepRequests,
      footstepBroadcasts = performanceData.network.footstepBroadcasts,
      validationFailures = performanceData.network.validationFailures,
      validationFailureRate = PerformanceMonitor.CalculateValidationFailureRate(),
      bytesTransmitted = performanceData.network.bytesTransmitted,
      requestsPerSecond = PerformanceMonitor.CalculateRequestsPerSecond(),
    },

    materials = {
      resolutionAttempts = performanceData.materials.resolutionAttempts,
      customMaterialHits = performanceData.materials.customMaterialHits,
      robloxMaterialHits = performanceData.materials.robloxMaterialHits,
      defaultMaterialHits = performanceData.materials.defaultMaterialHits,
      attributeLookups = performanceData.materials.attributeLookups,
      raycastOperations = performanceData.materials.raycastOperations,
    },

    performance = {
      uptime = PerformanceMonitor.CalculateUptime(),
      averageFrameTime = performanceData.performance.averageFrameTime,
      peakFrameTime = performanceData.performance.peakFrameTime,
      memoryUsage = performanceData.performance.memoryUsage,
    },

    errors = {
      soundPlaybackFailures = performanceData.errors.soundPlaybackFailures,
      networkErrors = performanceData.errors.networkErrors,
      materialResolutionErrors = performanceData.errors.materialResolutionErrors,
      validationErrors = performanceData.errors.validationErrors,
    },
  }
end

function PerformanceMonitor.GetSummary()
  local metrics = PerformanceMonitor.GetMetrics()

  return {
    uptime = metrics.performance.uptime,
    activeSounds = metrics.sounds.currentActive,
    totalRequests = metrics.network.footstepRequests,
    poolEfficiency = metrics.sounds.poolEfficiency,
    validationFailureRate = metrics.network.validationFailureRate,
    requestsPerSecond = metrics.network.requestsPerSecond,
    memoryUsage = metrics.performance.memoryUsage,
    totalErrors = metrics.errors.soundPlaybackFailures
      + metrics.errors.networkErrors
      + metrics.errors.materialResolutionErrors
      + metrics.errors.validationErrors,
  }
end

function PerformanceMonitor.PrintReport()
  local metrics = PerformanceMonitor.GetMetrics()

  print("=== MAFS Performance Report ===")
  print(string.format("Uptime: %.1f seconds", metrics.performance.uptime))
  print("")

  print("Sound System:")
  print(
    string.format(
      "  Active Sounds: %d (Max: %d)",
      metrics.sounds.currentActive,
      metrics.sounds.maxConcurrent
    )
  )
  print(string.format("  Total Created: %d", metrics.sounds.totalCreated))
  print(string.format("  Pool Efficiency: %.1f%%", metrics.sounds.poolEfficiency * 100))
  print("")

  print("Network:")
  print(string.format("  Footstep Requests: %d", metrics.network.footstepRequests))
  print(string.format("  Requests/Second: %.1f", metrics.network.requestsPerSecond))
  print(
    string.format(
      "  Validation Failures: %d (%.1f%%)",
      metrics.network.validationFailures,
      metrics.network.validationFailureRate * 100
    )
  )
  print(string.format("  Bytes Transmitted: %.1f KB", metrics.network.bytesTransmitted / 1024))
  print("")

  print("Materials:")
  print(string.format("  Resolution Attempts: %d", metrics.materials.resolutionAttempts))
  print(string.format("  Custom Material Hits: %d", metrics.materials.customMaterialHits))
  print(string.format("  Roblox Material Hits: %d", metrics.materials.robloxMaterialHits))
  print(string.format("  Default Material Hits: %d", metrics.materials.defaultMaterialHits))
  print("")

  print("Performance:")
  print(string.format("  Average Frame Time: %.3f ms", metrics.performance.averageFrameTime * 1000))
  print(string.format("  Peak Frame Time: %.3f ms", metrics.performance.peakFrameTime * 1000))
  print(string.format("  Memory Usage: %.1f MB", metrics.performance.memoryUsage / (1024 * 1024)))
  print("")

  local totalErrors = metrics.errors.soundPlaybackFailures
    + metrics.errors.networkErrors
    + metrics.errors.materialResolutionErrors
    + metrics.errors.validationErrors
  print(string.format("Total Errors: %d", totalErrors))
  if totalErrors > 0 then
    print(string.format("  Sound Playback Failures: %d", metrics.errors.soundPlaybackFailures))
    print(string.format("  Network Errors: %d", metrics.errors.networkErrors))
    print(
      string.format("  Material Resolution Errors: %d", metrics.errors.materialResolutionErrors)
    )
    print(string.format("  Validation Errors: %d", metrics.errors.validationErrors))
  end
end

function PerformanceMonitor.Reset()
  performanceData = {
    sounds = {
      totalCreated = 0,
      totalDestroyed = 0,
      currentActive = 0,
      poolHits = 0,
      poolMisses = 0,
      maxConcurrent = 0,
    },
    network = {
      footstepRequests = 0,
      footstepBroadcasts = 0,
      validationFailures = 0,
      bytesTransmitted = 0,
    },
    materials = {
      resolutionAttempts = 0,
      customMaterialHits = 0,
      robloxMaterialHits = 0,
      defaultMaterialHits = 0,
      attributeLookups = 0,
      raycastOperations = 0,
    },
    performance = {
      averageFrameTime = 0,
      peakFrameTime = 0,
      memoryUsage = 0,
      startTime = tick(),
      lastUpdateTime = tick(),
    },
    errors = {
      soundPlaybackFailures = 0,
      networkErrors = 0,
      materialResolutionErrors = 0,
      validationErrors = 0,
    },
  }
end

--[[
    Initialize performance monitoring
]]
function PerformanceMonitor.Initialize()
  if MAFSConfig.Settings.EnablePerformanceMetrics then
    -- Set up periodic updates
    RunService.Heartbeat:Connect(function()
      local currentTime = tick()
      if currentTime - lastUpdateTime >= UPDATE_INTERVAL then
        PerformanceMonitor.Update()
        lastUpdateTime = currentTime
      end
    end)

    print("MAFS: Performance monitoring initialized")
  end
end

return PerformanceMonitor

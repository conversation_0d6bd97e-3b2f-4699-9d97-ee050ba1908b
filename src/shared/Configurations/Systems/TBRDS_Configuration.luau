--[[
    TBRDS Configuration Module

    Central configuration for the Tag-Based Role Display System (TBRDS)

    ARCHITECTURE ROLE:
    - Provides centralized system settings and constants
    - Manages role priority and validation rules
    - Enables debug mode and performance monitoring
    - Defines security and rate limiting parameters

    USAGE:
    - Import this module to access system configurations
    - Used by both client and server components

    *Dynamic Innovative Studio*
]]

local TBRDSConfig = {}

-- System Settings
TBRDSConfig.Settings = {
  DebugMode = false,
  EnablePerformanceMetrics = true,
  EnableEventSystem = true,

  -- Security Settings
  RateLimit = {
    Window = 60, -- seconds
    MaxRequests = 5, -- requests per window
  },

  -- Validation Settings
  MaxTagLength = 50,
  MaxDisplayNameLength = 100,

  -- Performance Settings
  TagValidationInterval = 60, -- seconds
  GroupRankCheckInterval = 30, -- seconds
  MaxCachedPlayers = 200,

  -- Display Settings
  BillboardSettings = {
    MaxDistance = 100,
    StudsOffset = Vector3.new(0, 3.5, 0),
    Size = UDim2.new(8, 0, 2, 0),
    AlwaysOnTop = true,
    LightInfluence = 1,
  },

  -- Anti-Exploit Settings
  AntiExploit = {
    EnablePositionValidation = true,
    EnableRoleValidation = true,
    EnableDataStoreValidation = true,
    MaxRoleChangesPerMinute = 3,
    SuspiciousActivityThreshold = 10,
  },
}

-- Group Configuration
TBRDSConfig.Groups = {
  Primary = {
    Id = 34320208,
    Name = "Dynamic Innovative Studio",
  },
}

-- Role Priority Configuration (highest to lowest)
TBRDSConfig.RolePriority = {
  "BleckWolf25", -- Founder (highest priority)
  "Anonmancer", -- Co-Founder
  "Senior Moderator",
  "Game Moderator",
  "Junior Moderator",
  "Developer",
  "Investors",
  "Trusted",
  "Supporter",
  "User", -- Default role (lowest priority)
}

-- GamePass Configuration
TBRDSConfig.GamePasses = {
  Supporter = 99445069658101,
}

-- Event Names
TBRDSConfig.Events = {
  TagChanged = "TagChanged",
  TagAssigned = "TagAssigned",
  TagRemoved = "TagRemoved",
  RoleValidated = "RoleValidated",
  SecurityViolation = "SecurityViolation",
}

-- Remote Event Names
TBRDSConfig.Remotes = {
  TagUpdate = "TagRemote",
  TagRequest = "TagRequestRemote",
  TagValidation = "TagValidationRemote",
  SecurityReport = "SecurityReportRemote",
}

-- Error Codes
TBRDSConfig.ErrorCodes = {
  RATE_LIMIT_EXCEEDED = "RATE_LIMIT_EXCEEDED",
  INVALID_PLAYER = "INVALID_PLAYER",
  INVALID_ROLE = "INVALID_ROLE",
  SECURITY_VIOLATION = "SECURITY_VIOLATION",
  DATASTORE_ERROR = "DATASTORE_ERROR",
  VALIDATION_FAILED = "VALIDATION_FAILED",
}

-- Performance Metrics Configuration
TBRDSConfig.Metrics = {
  TrackTagAssignments = true,
  TrackValidationTime = true,
  TrackSecurityEvents = true,
  TrackPerformance = true,
  MetricsRetentionTime = 3600, -- 1 hour in seconds
}

-- Debug Configuration
TBRDSConfig.Debug = {
  LogTagAssignments = true,
  LogValidationEvents = true,
  LogSecurityEvents = true,
  LogPerformanceMetrics = false,
  VerboseLogging = false,
}

-- Validation Rules
TBRDSConfig.ValidationRules = {
  RequireCharacter = true,
  RequireValidUserId = true,
  RequireGroupMembership = false, -- For default users
  ValidateRolePermissions = true,
  CheckGamePassOwnership = true,
}

-- Cache Configuration
TBRDSConfig.Cache = {
  PlayerDataTTL = 300, -- 5 minutes
  RoleDataTTL = 600, -- 10 minutes
  GroupDataTTL = 1800, -- 30 minutes
  MaxCacheSize = 1000,
}

-- Integration Settings
TBRDSConfig.Integration = {
  EnableMAFSIntegration = true,
  EnableMCSIntegration = true,
  EnableCGSIntegration = true,
  BroadcastToOtherSystems = true,
}

return TBRDSConfig

--!strict
--[[
	TBRDS Utils Module
	- Consistent debug logging with configuration check
    - Rate limiter
]]

-- Services
local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- Config reference (can be nil during testing/development)
local Configuration = ReplicatedStorage:FindFirstChild("Configurations")
  and ReplicatedStorage.Configurations:FindFirstChild("Systems")
  and ReplicatedStorage.Configurations.Systems:FindFirstChild("TBRDS")

-- Utils
local Utils = {}

-- Check if Debug Mode is enabled
function Utils.isDebugMode()
  -- Default to false if Configuration is not found
  if not Configuration then
    return false
  end

  return Configuration:GetAttribute("DebugMode") == true
end

-- Debug print function with module identifier
function Utils.print(moduleName, message)
  if Utils.isDebugMode() then
    print(string.format("[TBRDS:%s]: %s", moduleName, message))
  end
end

-- Create a rate limiter
-- window: time window in seconds
-- limit: max number of requests allowed in window
function Utils.createRateLimiter(window, limit)
  local limiter = {
    Window = window,
    Limit = limit,
    Requests = {}, -- {[userId] = {timestamps}}
  }

  -- Check if a request is allowed
  function limiter:CheckLimit(userId)
    -- Initialize if needed
    self.Requests[userId] = self.Requests[userId] or {}

    -- Current time
    local now = os.time()

    -- Remove old timestamps
    local validTimestamps = {}
    for _, timestamp in ipairs(self.Requests[userId]) do
      if now - timestamp < self.Window then
        table.insert(validTimestamps, timestamp)
      end
    end

    -- Update timestamps
    self.Requests[userId] = validTimestamps

    -- Check if under limit
    local count = #self.Requests[userId]
    if count < self.Limit then
      -- Add current timestamp
      table.insert(self.Requests[userId], now)
      return true, count + 1
    else
      -- Over limit
      return false, count
    end
  end

  return limiter
end

-- Function to find a descendant by name (case-insensitive)
function Utils.findDescendantIgnoreCase(parent, name)
  -- Make the string lower (to be case-insensitive)
  name = string.lower(name)

  -- For each child that matches the string, return it
  for _, child in ipairs(parent:GetDescendants()) do
    -- Make the child a game object with a name
    if
      typeof(child) == "Instance" and child:IsA("BasePart")
      or child:IsA("GuiObject")
      or child:IsA("Model")
    then
      if string.lower(child.Name) == name then
        return child -- Return the child with a name
      end
    end
  end
  return nil -- This should not be an issue, when you remove !--strict it disappears
end

-- Function to create a simple text label
function Utils.createTextLabel(
  parent,
  name,
  text,
  textColor,
  font,
  textSize,
  backgroundTransparency
)
  local label = Instance.new("TextLabel")
  label.Name = name
  label.Parent = parent
  label.Text = text
  label.TextColor3 = textColor or Color3.new(1, 1, 1)
  label.Font = font or Enum.Font.SourceSans
  label.TextSize = textSize or 12
  label.BackgroundTransparency = backgroundTransparency or 1
  label.TextScaled = false
  label.TextSize = 24
  return label
end

-- Function to create a simple image label
function Utils.createImageLabel(parent, name, image, size, position, backgroundTransparency)
  local imageLabel = Instance.new("ImageLabel")
  imageLabel.Name = name
  imageLabel.Parent = parent
  imageLabel.Image = image
  imageLabel.Size = size or UDim2.new(1, 0, 1, 0)
  imageLabel.Position = position or UDim2.new(0, 0, 0, 0)
  imageLabel.BackgroundTransparency = backgroundTransparency or 1
  return imageLabel
end

return Utils

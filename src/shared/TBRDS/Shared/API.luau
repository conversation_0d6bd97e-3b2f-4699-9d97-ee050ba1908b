--!strict
--[[
    TBRDS Public API Module

    Public interface for external systems to interact with TBRDS

    ARCHITECTURE ROLE:
    - Provides clean API for other systems to use TBRDS
    - Abstracts internal implementation details
    - Enables integration with MAFS, MCS, CGS, and other systems
    - Provides event subscription for system reactions

    USAGE:
    - Import this module to interact with TBRDS
    - Subscribe to tag events for system integration
    - Query player roles and tag data

    *Dynamic Innovative Studio*
]]

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")

-- Import modules
local TBRDSConfig = require(ReplicatedStorage.Configurations.Systems.TBRDS_Configuration)
local Types = require(script.Parent.Types)
local EventSystem = require(script.Parent.EventSystem)
local PerformanceMonitor = require(script.Parent.PerformanceMonitor)
local Utils = require(script.Parent.Utils)

type TBRDSApi = Types.TBRDSApi
type ValidationResult = Types.ValidationResult
type PlayerTagData = Types.PlayerTagData
type PerformanceMetrics = Types.PerformanceMetrics
type RoleStyle = Types.RoleStyle
type EventCallback = Types.EventCallback

local TBRDSAPI = {}

-- Internal state (would be managed by the actual TBRDS system)
local playerTagCache: {[Player]: PlayerTagData} = {}
local roleHandlers = nil -- This would be set by the main TBRDS system

-- Debug logging
local function debugLog(message: string)
  if TBRDSConfig.Settings.DebugMode then
    print(string.format("[TBRDS:API]: %s", message))
  end
end

-- Initialize the API (called by main TBRDS system)
function TBRDSAPI._Initialize(roleHandlersModule)
  roleHandlers = roleHandlersModule
  debugLog("API initialized")
end

-- Get a player's current role
function TBRDSAPI.GetPlayerRole(player: Player): string
  local validation = Utils.validatePlayer(player)
  if not validation.Success then
    return "User" -- Default role
  end
  
  -- Check cache first
  local cachedData = playerTagCache[player]
  if cachedData then
    PerformanceMonitor.RecordCacheHit()
    return cachedData.Role
  end
  
  PerformanceMonitor.RecordCacheMiss()
  
  -- Get role from role handlers if available
  if roleHandlers and roleHandlers.GetPlayerRole then
    local role = roleHandlers.GetPlayerRole(player)
    return role or "User"
  end
  
  return "User"
end

-- Set a player's role (server-side only)
function TBRDSAPI.SetPlayerRole(player: Player, roleName: string): ValidationResult
  local result: ValidationResult = {
    Success = false,
    Role = nil,
    ErrorCode = nil,
    ErrorMessage = nil,
    SecurityFlags = nil,
  }
  
  -- Server-side only
  if not RunService:IsServer() then
    result.ErrorCode = TBRDSConfig.ErrorCodes.SECURITY_VIOLATION
    result.ErrorMessage = "SetPlayerRole can only be called on the server"
    return result
  end
  
  -- Validate player
  local playerValidation = Utils.validatePlayer(player)
  if not playerValidation.Success then
    return playerValidation
  end
  
  -- Validate role
  if not Utils.validateRole(roleName) then
    result.ErrorCode = TBRDSConfig.ErrorCodes.INVALID_ROLE
    result.ErrorMessage = string.format("Invalid role: %s", roleName)
    return result
  end
  
  -- Security validation
  local securityFlags = Utils.validateSecurity(player, "SetPlayerRole")
  if securityFlags then
    result.ErrorCode = TBRDSConfig.ErrorCodes.SECURITY_VIOLATION
    result.ErrorMessage = "Security validation failed"
    result.SecurityFlags = securityFlags
    return result
  end
  
  -- Update cache
  local oldRole = playerTagCache[player] and playerTagCache[player].Role
  playerTagCache[player] = {
    Role = roleName,
    BillboardGUI = nil,
    LastUpdated = os.time(),
    ValidationCount = (playerTagCache[player] and playerTagCache[player].ValidationCount or 0) + 1,
    SecurityFlags = nil,
  }
  
  -- Fire event
  local eventData = EventSystem.CreateEventData(
    player,
    roleName,
    oldRole,
    "API.SetPlayerRole"
  )
  EventSystem.Fire("TagChanged", eventData)
  
  PerformanceMonitor.RecordTagAssignment(roleName)
  
  result.Success = true
  result.Role = roleName
  return result
end

-- Refresh a player's tag (re-validate and update)
function TBRDSAPI.RefreshPlayerTag(player: Player): ValidationResult
  local result: ValidationResult = {
    Success = false,
    Role = nil,
    ErrorCode = nil,
    ErrorMessage = nil,
    SecurityFlags = nil,
  }
  
  -- Validate player
  local playerValidation = Utils.validatePlayer(player)
  if not playerValidation.Success then
    return playerValidation
  end
  
  -- Get current role from role handlers
  if not roleHandlers or not roleHandlers.GetPlayerRole then
    result.ErrorCode = TBRDSConfig.ErrorCodes.VALIDATION_FAILED
    result.ErrorMessage = "Role handlers not available"
    return result
  end
  
  local startTime = tick()
  local currentRole = roleHandlers.GetPlayerRole(player)
  local validationTime = (tick() - startTime) * 1000
  
  PerformanceMonitor.RecordValidationTime(validationTime)
  
  -- Update cache
  local oldRole = playerTagCache[player] and playerTagCache[player].Role
  playerTagCache[player] = {
    Role = currentRole,
    BillboardGUI = playerTagCache[player] and playerTagCache[player].BillboardGUI,
    LastUpdated = os.time(),
    ValidationCount = (playerTagCache[player] and playerTagCache[player].ValidationCount or 0) + 1,
    SecurityFlags = nil,
  }
  
  -- Fire event if role changed
  if oldRole ~= currentRole then
    local eventData = EventSystem.CreateEventData(
      player,
      currentRole,
      oldRole,
      "API.RefreshPlayerTag"
    )
    EventSystem.Fire("TagChanged", eventData)
    PerformanceMonitor.RecordTagAssignment(currentRole)
  end
  
  result.Success = true
  result.Role = currentRole
  return result
end

-- Get player tag data
function TBRDSAPI.GetPlayerTagData(player: Player): PlayerTagData?
  local validation = Utils.validatePlayer(player)
  if not validation.Success then
    return nil
  end
  
  return playerTagCache[player]
end

-- Check if a role is valid for a player
function TBRDSAPI.IsRoleValid(player: Player, roleName: string): boolean
  local validation = Utils.validatePlayer(player)
  if not validation.Success then
    return false
  end
  
  if not Utils.validateRole(roleName) then
    return false
  end
  
  -- Check with role handlers if available
  if roleHandlers and roleHandlers[roleName] and roleHandlers[roleName].Check then
    return roleHandlers[roleName].Check(player)
  end
  
  return false
end

-- Get role style information
function TBRDSAPI.GetRoleStyle(roleName: string): RoleStyle?
  if not Utils.validateRole(roleName) then
    return nil
  end
  
  if roleHandlers and roleHandlers[roleName] then
    return roleHandlers[roleName].Style
  end
  
  return nil
end

-- Subscribe to tag change events
function TBRDSAPI.SubscribeToTagChanges(callback: EventCallback): string
  return EventSystem.Subscribe("TagChanged", callback)
end

-- Unsubscribe from tag change events
function TBRDSAPI.UnsubscribeFromTagChanges(subscriptionId: string): boolean
  return EventSystem.Unsubscribe(subscriptionId)
end

-- Get performance metrics
function TBRDSAPI.GetPerformanceMetrics(): PerformanceMetrics
  return PerformanceMonitor.GetMetrics()
end

-- Set debug mode
function TBRDSAPI.SetDebugMode(enabled: boolean): ()
  -- This would update the configuration in a real implementation
  debugLog(string.format("Debug mode %s", enabled and "enabled" or "disabled"))
end

-- Get all players with a specific role
function TBRDSAPI.GetPlayersWithRole(roleName: string): {Player}
  local players = {}
  
  for player, tagData in pairs(playerTagCache) do
    if tagData.Role == roleName then
      table.insert(players, player)
    end
  end
  
  return players
end

-- Get role statistics
function TBRDSAPI.GetRoleStatistics(): {[string]: number}
  local stats = {}
  
  for _, tagData in pairs(playerTagCache) do
    stats[tagData.Role] = (stats[tagData.Role] or 0) + 1
  end
  
  return stats
end

-- Check system health
function TBRDSAPI.GetSystemHealth(): {[string]: any}
  local health = {
    playersTracked = 0,
    cacheSize = 0,
    performanceMetrics = PerformanceMonitor.GetMetrics(),
    eventSystemMetrics = EventSystem.GetMetrics(),
    configurationValid = true,
  }
  
  -- Count tracked players
  for _ in pairs(playerTagCache) do
    health.playersTracked = health.playersTracked + 1
  end
  
  health.cacheSize = health.playersTracked
  
  return health
end

-- Internal function to update player cache (used by main TBRDS system)
function TBRDSAPI._UpdatePlayerCache(player: Player, tagData: PlayerTagData): ()
  playerTagCache[player] = tagData
end

-- Internal function to remove player from cache (used by main TBRDS system)
function TBRDSAPI._RemovePlayerFromCache(player: Player): ()
  playerTagCache[player] = nil
end

-- Internal function to get the full cache (for debugging)
function TBRDSAPI._GetPlayerCache(): {[Player]: PlayerTagData}
  return playerTagCache
end

debugLog("API module loaded")

return TBRDSAPI

--[[
    AutocompleteService

    Provides suggestions for commands and arguments:
    - Command name suggestions
    - Player name suggestions
    - Command-specific argument suggestions
]]

-- Services
local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- Modules
local CommandParser = require(script.Parent.CommandParser)
local Constants = require(ReplicatedStorage.MCS.Shared.Constants)
local Utils = require(ReplicatedStorage.MCS.Shared.Utils)

-- Autocomplete Service data store
local AutocompleteService = {}

-- Debug logging
local function debugLog(message)
  Utils.print("AutocompleteService", message)
end

-- Store remote function reference
local autocompleteRemote = nil

-- Local cache of suggestions to reduce server calls
local suggestionCache = {
  commands = {}, -- Command suggestions
  lastCommandUpdate = 0, -- Last time commands were updated
  players = {}, -- Player name suggestions (updated in real-time)
  commandSpecific = {}, -- Command-specific suggestions
  lastCommandSpecificUpdate = {}, -- Last time specific command suggestions were updated
}

-- Cache durations
local CACHE_DURATION = {
  COMMANDS = 60, -- 1 minute for commands
  COMMAND_SPECIFIC = 30, -- 30 seconds for command-specific suggestions
}

-- Initialize the service
function AutocompleteService.init(remote)
  debugLog("Initializing service")
  autocompleteRemote = remote

  -- Set up player suggestion cache
  local function updatePlayerSuggestions()
    local newSuggestions = {}
    for _, player in ipairs(Players:GetPlayers()) do
      table.insert(newSuggestions, player.Name)
    end
    suggestionCache.players = newSuggestions
    debugLog("Updated player suggestions: " .. #newSuggestions .. " players")
  end

  -- Update initially
  updatePlayerSuggestions()

  -- Update when players join/leave
  Players.PlayerAdded:Connect(function()
    updatePlayerSuggestions()
  end)

  Players.PlayerRemoving:Connect(function()
    updatePlayerSuggestions()
  end)

  debugLog("Service initialized successfully")
end

-- Get suggestions for the current command input
function AutocompleteService.getSuggestions(text: string): { string }
  Utils.startTimer("getSuggestions")

  if not CommandParser.isCommand(text) then
    Utils.endTimer("AutocompleteService", "getSuggestions")
    return {}
  end

  -- Parse command text
  local commandName, args = CommandParser.parse(text)

  -- If no command name yet, suggest commands
  if not commandName or #commandName == 0 then
    local suggestions = AutocompleteService.getCommandSuggestions(text)
    Utils.endTimer("AutocompleteService", "getSuggestions")
    return suggestions
  end

  -- If command name but no args yet, suggest commands or first arg
  if not args or #args == 0 then
    local commandMatches = AutocompleteService.getCommandSuggestions(text)
    if #commandMatches > 0 then
      Utils.endTimer("AutocompleteService", "getSuggestions")
      return commandMatches
    end

    -- If no command matches, suggest first arg
    local suggestions = AutocompleteService.getArgumentSuggestions(commandName, 1, "")
    Utils.endTimer("AutocompleteService", "getSuggestions")
    return suggestions
  end

  -- If command and args, suggest next arg
  local currentArgIndex = #args
  local currentArgText = args[currentArgIndex] or ""

  -- Only suggest for the last argument being typed
  local suggestions =
    AutocompleteService.getArgumentSuggestions(commandName, currentArgIndex, currentArgText)
  Utils.endTimer("AutocompleteService", "getSuggestions")
  return suggestions
end

-- Get command name suggestions
function AutocompleteService.getCommandSuggestions(partialText: string): { string }
  debugLog("Getting command suggestions for: " .. partialText)
  Utils.startTimer("getCommandSuggestions")

  -- Update command suggestions from server if cache is old
  local currentTime = os.time()
  if
    #suggestionCache.commands == 0
    or currentTime - suggestionCache.lastCommandUpdate > CACHE_DURATION.COMMANDS
  then
    -- Only update if remote is set
    if autocompleteRemote then
      local success, commands = pcall(function()
        return autocompleteRemote:InvokeServer("commands", "")
      end)

      if success and typeof(commands) == "table" then
        suggestionCache.commands = commands
        suggestionCache.lastCommandUpdate = currentTime
        debugLog("Updated command suggestions from server: " .. #commands .. " commands")
      else
        debugLog("Failed to update command suggestions from server")
      end
    end
  end

  -- Filter commands based on partial text
  local matches = {}
  local partialCommand = partialText:match("^%s*" .. Constants.Prefix .. "(.*)$") or ""
  local lowerPartial = partialCommand:lower()

  for _, command in ipairs(suggestionCache.commands) do
    if command:lower():sub(1, #lowerPartial) == lowerPartial then
      table.insert(matches, Constants.Prefix .. command)
    end
  end

  debugLog("Found " .. #matches .. " command matches")
  Utils.endTimer("AutocompleteService", "getCommandSuggestions")
  return matches
end

-- Get argument suggestions (players, command-specific, etc.)
function AutocompleteService.getArgumentSuggestions(
  commandName: string,
  argIndex: number,
  partialArg: string
): { string }
  debugLog("Getting arg suggestions for command: " .. commandName .. ", argIndex: " .. argIndex)
  Utils.startTimer("getArgumentSuggestions")

  -- Depending on command and argument position, suggest differently
  local suggestions = {}

  -- Try to get command-specific suggestions from server
  local commandKey = commandName .. "_" .. argIndex
  local currentTime = os.time()

  -- Check if we need to update command-specific suggestions
  local shouldUpdate = not suggestionCache.commandSpecific[commandKey]
    or not suggestionCache.lastCommandSpecificUpdate[commandKey]
    or currentTime - suggestionCache.lastCommandSpecificUpdate[commandKey]
      > CACHE_DURATION.COMMAND_SPECIFIC

  if shouldUpdate and autocompleteRemote then
    local success, specific = pcall(function()
      return autocompleteRemote:InvokeServer(
        "arg",
        { command = commandName, index = argIndex, partial = partialArg }
      )
    end)

    if success and typeof(specific) == "table" then
      suggestionCache.commandSpecific[commandKey] = specific
      suggestionCache.lastCommandSpecificUpdate[commandKey] = currentTime
      debugLog("Updated command-specific suggestions from server: " .. #specific .. " suggestions")
    else
      debugLog("Failed to update command-specific suggestions from server")
    end
  end

  -- Add command-specific suggestions first
  if suggestionCache.commandSpecific[commandKey] then
    for _, suggestion in ipairs(suggestionCache.commandSpecific[commandKey]) do
      if suggestion:lower():sub(1, #partialArg:lower()) == partialArg:lower() then
        table.insert(suggestions, suggestion)
      end
    end
  end

  -- For many admin commands, the first argument is often a player name
  local commonPlayerCommands = {
    "ban",
    "kick",
    "teleport",
    "warn",
    "mute",
    "unmute",
    "freeze",
    "unfreeze",
    "bring",
    "goto",
    "message",
    "pm",
  }

  -- Check if this command typically uses player names
  local isPlayerCommand = false
  for _, cmd in ipairs(commonPlayerCommands) do
    if commandName:lower() == cmd then
      isPlayerCommand = true
      break
    end
  end

  -- If it's a player command, recommend player names
  if isPlayerCommand and argIndex == 1 then
    for _, playerName in ipairs(suggestionCache.players) do
      if playerName:lower():sub(1, #partialArg:lower()) == partialArg:lower() then
        table.insert(suggestions, playerName)
      end
    end
  end

  -- If no specific suggestions, add generic common values
  if #suggestions == 0 then
    -- Add common boolean values
    local booleanValues = { "true", "false", "yes", "no", "on", "off" }
    for _, value in ipairs(booleanValues) do
      if value:sub(1, #partialArg:lower()) == partialArg:lower() then
        table.insert(suggestions, value)
      end
    end
  end

  -- Limit to maximum allowed suggestions
  if #suggestions > Constants.MAX_SUGGESTIONS then
    suggestions = { unpack(suggestions, 1, Constants.MAX_SUGGESTIONS) }
  end

  debugLog("Found " .. #suggestions .. " argument suggestions")
  Utils.endTimer("AutocompleteService", "getArgumentSuggestions")
  return suggestions
end

debugLog("Module initialized")
return AutocompleteService

--[[
    Analytics Middleware
    Tracks command usage patterns and player behavior
]]

local Analytics = {}
local AnalyticsService = game:GetService("DataStoreService"):GetDataStore("CommandAnalytics")

-- Command statistics format: {[commandName] = {count = 0, lastUsed = 0}}
local commandStats = {}

function Analytics.process(player, commandName, args)
  -- Update command statistics
  commandStats[commandName] = commandStats[commandName] or { count = 0, lastUsed = os.time() }
  commandStats[commandName].count += 1
  commandStats[commandName].lastUsed = os.time()

  -- Periodic save to DataStore (every 5 minutes)
  if os.time() % 300 == 0 then -- 5 minute intervals
    task.spawn(function()
      pcall(function()
        AnalyticsService:SetAsync("command_stats", commandStats)
      end)
    end)
  end

  return true
end

-- Periodically save analytics
task.spawn(function()
  while true do
    task.wait(300) -- 5 minutes
    pcall(function()
      AnalyticsService:SetAsync("command_stats", commandStats)
    end)
  end
end)

return Analytics

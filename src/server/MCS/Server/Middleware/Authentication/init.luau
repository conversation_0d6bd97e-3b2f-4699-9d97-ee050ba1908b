--[[
    Authentication Middleware

    Validates player identity and checks if they have permission to use commands.
    Works with PermissionService to enforce access control.
]]

local Authentication = {}

local ServerScriptService = game:GetService("ServerScriptService")
local PermissionService = require(ServerScriptService.MCS.Server.PermissionService)

function Authentication.process(player, commandName, args)
  -- Basic player verification
  if not player or not player.Parent then
    return false, "Invalid player"
  end

  -- Check if player has permission to use this command
  if not PermissionService.canUseCommand(player, commandName) then
    return false, "You don't have permission to use this command"
  end

  return true
end

return Authentication

--[[
    PermissionService

    Manages authorization for command execution:
    - Defines permission roles and levels
    - Checks if players can execute specific commands
    - Provides caching for performance optimization
]]

local DataStoreService = game:GetService("DataStoreService")
local Players = game:GetService("Players")

local PermissionService = {}

-- Permission levels
PermissionService.Levels = {
  PLAYER = 1, -- Standard player level (added)
  DISDevelopers = 10,
  JuniorModerators = 25,
  GameModerators = 50,
  SeniorModerators = 100,
  <PERSON><PERSON><PERSON><PERSON> = 200,
  BleckWolf25 = 200,
}

-- Command permission requirements (default = PLAYER)
local commandPermissions = {}

-- Permission cache to avoid frequent group rank checks
-- format: {userId = {level = permissionLevel, lastUpdated = timestamp}}
local permissionCache = {}

-- Configuration
local CONFIG = {
  -- Default group ID for permissions
  GROUP_ID = 34320208, -- Set to 0 to disable group-based permissions

  -- Map group ranks to permission levels
  RANK_TO_LEVEL = {
    [255] = PermissionService.Levels.BleckWolf25, -- Founder rank
    [254] = PermissionService.Levels.Anonmancer, -- Co-Founder rank
    [253] = PermissionService.Levels.SeniorModerators, -- Senior Moderators rank
    [252] = PermissionService.Levels.GameModerators, -- Game Moderators rank
    [250] = PermissionService.Levels.JuniorModerators, -- Junior Moderators rank
    [249] = PermissionService.Levels.DISDevelopers, -- D.I.S Developers Rank
  },

  -- Special users with specific permission levels
  SPECIAL_USERS = {},

  -- Cache duration in seconds
  CACHE_DURATION = 300, -- 5 minutes
}

-- Initialize the service
function PermissionService.init()
  -- Set up cleanup for when players leave
  Players.PlayerRemoving:Connect(function(player)
    permissionCache[player.UserId] = nil
  end)

  -- Set default command permissions
  -- This can be expanded when commands are registered
  print("PermissionService initialized")
end

-- Register permission requirement for a command
function PermissionService.registerCommandPermission(commandName, permissionLevel)
  commandPermissions[commandName:lower()] = permissionLevel
end

-- Get permission level for a command
function PermissionService.getCommandPermissionLevel(commandName)
  return commandPermissions[commandName:lower()] or PermissionService.Levels.PLAYER
end

-- Get permission level for a player
function PermissionService.getPermissionLevel(player)
  local userId = player.UserId

  -- Check cache first
  if permissionCache[userId] then
    local cacheData = permissionCache[userId]
    if os.time() - cacheData.lastUpdated < CONFIG.CACHE_DURATION then
      return cacheData.level
    end
  end

  -- Check special users
  local level = CONFIG.SPECIAL_USERS[userId]

  -- Check group rank if no special permission
  if not level and CONFIG.GROUP_ID > 0 then
    local rank = player:GetRankInGroup(CONFIG.GROUP_ID)
    local highestLevel = PermissionService.Levels.PLAYER

    -- Find the highest permission level for the player's rank
    for rankValue, permLevel in pairs(CONFIG.RANK_TO_LEVEL) do
      if rank >= rankValue and permLevel > highestLevel then
        highestLevel = permLevel
      end
    end

    level = highestLevel
  end

  -- Default to PLAYER level
  level = level or PermissionService.Levels.PLAYER

  -- Update cache
  permissionCache[userId] = {
    level = level,
    lastUpdated = os.time(),
  }

  return level
end

-- Check if player can use a specific command
function PermissionService.canUseCommand(player, commandName)
  local requiredLevel = PermissionService.getCommandPermissionLevel(commandName)
  local playerLevel = PermissionService.getPermissionLevel(player)

  return playerLevel >= requiredLevel
end

-- Set permission level for a specific command
function PermissionService.setCommandPermission(commandName, level)
  commandPermissions[commandName:lower()] = level
end

-- Get all commands accessible by player's permission level
function PermissionService.getAccessibleCommands(player)
  local playerLevel = PermissionService.getPermissionLevel(player)
  local accessibleCommands = {}

  for command, requiredLevel in pairs(commandPermissions) do
    if playerLevel >= requiredLevel then
      table.insert(accessibleCommands, command)
    end
  end

  return accessibleCommands
end

return PermissionService

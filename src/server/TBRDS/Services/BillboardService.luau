--!strict
--[[
    TBRDS Billboard Service

    Billboard creation and management service for TBRDS

    ARCHITECTURE ROLE:
    - Creates and manages player tag billboards
    - Handles billboard styling and positioning
    - Manages billboard lifecycle and cleanup
    - Provides billboard-related utilities

    RESPONSIBILITIES:
    - Create billboards with proper styling
    - Update billboard content and appearance
    - Handle billboard positioning and visibility
    - Clean up billboards when players leave
    - Manage billboard performance and optimization

    *Dynamic Innovative Studio*
]]

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local Players = game:GetService("Players")

-- Import modules
local Types = require(ReplicatedStorage.TBRDS.Shared.Types)
local Utils = require(ReplicatedStorage.TBRDS.Shared.Utils)
local PerformanceMonitor = require(ReplicatedStorage.TBRDS.Shared.PerformanceMonitor)
local ConfigurationService = require(script.Parent.ConfigurationService)

type RoleStyle = Types.RoleStyle
type BillboardSettings = Types.BillboardSettings

local BillboardService = {}

-- Service state
local isInitialized = false
local activeBillboards: {[Player]: BillboardGui} = {}
local billboardSettings: BillboardSettings

-- Debug logging
local function debugLog(message: string)
  if ConfigurationService.IsDebugMode() then
    print(string.format("[TBRDS:BillboardService]: %s", message))
  end
end

-- Initialize the billboard service
function BillboardService.Initialize(): boolean
  if isInitialized then
    debugLog("Billboard service already initialized")
    return true
  end

  debugLog("Initializing billboard service...")

  -- Load billboard settings from configuration
  billboardSettings = ConfigurationService.GetBillboardSettings()

  -- Set up player cleanup
  Players.PlayerRemoving:Connect(function(player)
    BillboardService.RemoveBillboard(player)
  end)

  isInitialized = true
  debugLog("Billboard service initialized successfully")
  return true
end

-- Create a billboard for a player
function BillboardService.CreateBillboard(player: Player, roleName: string, style: RoleStyle): BillboardGui?
  if not player.Character then
    debugLog(string.format("Cannot create billboard for %s: no character", player.Name))
    return nil
  end

  local head = player.Character:FindFirstChild("Head")
  if not head then
    debugLog(string.format("Cannot create billboard for %s: no head", player.Name))
    return nil
  end

  -- Remove existing billboard if present
  BillboardService.RemoveBillboard(player)

  -- Create new billboard
  local billboard = Instance.new("BillboardGui")
  billboard.Name = "TagGui"
  billboard.Adornee = head
  billboard.Size = billboardSettings.Size
  billboard.StudsOffset = billboardSettings.StudsOffset
  billboard.AlwaysOnTop = billboardSettings.AlwaysOnTop
  billboard.MaxDistance = billboardSettings.MaxDistance
  billboard.Active = true
  billboard.ZIndexBehavior = Enum.ZIndexBehavior.Global
  billboard.LightInfluence = billboardSettings.LightInfluence
  billboard.Parent = head

  -- Create content
  local success = BillboardService.PopulateBillboard(billboard, player, roleName, style)
  if not success then
    billboard:Destroy()
    return nil
  end

  -- Store reference
  activeBillboards[player] = billboard

  debugLog(string.format("Created billboard for %s with role %s", player.Name, roleName))
  PerformanceMonitor.RecordTagAssignment(roleName)

  return billboard
end

-- Populate billboard with content
function BillboardService.PopulateBillboard(billboard: BillboardGui, player: Player, roleName: string, style: RoleStyle): boolean
  local success, err = pcall(function()
    -- Create a frame to hold the tag elements
    local frame = Instance.new("Frame")
    frame.Name = "TagFrame"
    frame.Size = UDim2.new(1, 0, 1, 0)
    frame.BackgroundTransparency = 1
    frame.Parent = billboard

    -- Add UIListLayout for horizontal arrangement
    local layout = Instance.new("UIListLayout")
    layout.FillDirection = Enum.FillDirection.Horizontal
    layout.HorizontalAlignment = Enum.HorizontalAlignment.Center
    layout.VerticalAlignment = Enum.VerticalAlignment.Center
    layout.SortOrder = Enum.SortOrder.LayoutOrder
    layout.Padding = UDim.new(0, 10) -- 10-pixel gap between image and text
    layout.Parent = frame

    -- Add icon if specified
    if style.Image then
      local icon = Instance.new("ImageLabel")
      icon.Name = "Icon"
      icon.Size = UDim2.new(0, 30, 0, 30) -- Fixed size for the image
      icon.BackgroundTransparency = 1
      icon.Image = style.Image
      icon.LayoutOrder = 1
      icon.ImageTransparency = 0
      icon.ScaleType = Enum.ScaleType.Fit
      icon.AnchorPoint = Vector2.new(0.5, 0.5)
      icon.Position = UDim2.new(0.5, 0, 0.5, 0)
      icon.Parent = frame
      
      debugLog(string.format("Added icon for %s: %s", player.Name, style.Image))
    end

    -- Create the tag label
    local label = Instance.new("TextLabel")
    label.Name = "TagLabel"
    label.Size = UDim2.new(0, 150, 0, 30) -- Fixed width, adjust as needed
    label.BackgroundTransparency = 1
    label.Text = style.GetText and style.GetText(player) or "[" .. roleName .. "]"
    label.TextColor3 = style.Color
    label.Font = style.Font
    label.TextScaled = true
    label.TextStrokeTransparency = 0.5
    label.LayoutOrder = 2
    label.Parent = frame

    -- Apply gradient if specified
    if style.Gradient then
      BillboardService.ApplyGradient(label, style.Gradient)
    end

    -- Apply text effects if specified
    if style.TextStroke then
      label.TextStrokeColor3 = style.TextStroke.Color
      label.TextStrokeTransparency = style.TextStroke.Transparency
    end

    -- Apply font style if specified
    if style.FontStyle and Enum.Font[style.FontStyle] then
      label.Font = Enum.Font[style.FontStyle]
    end
  end)

  if not success then
    warn(string.format("TBRDS: Failed to populate billboard for %s: %s", player.Name, tostring(err)))
    PerformanceMonitor.RecordError("BILLBOARD_POPULATION_ERROR")
    return false
  end

  return true
end

-- Apply gradient to a text label
function BillboardService.ApplyGradient(label: TextLabel, gradientStyle: Types.GradientStyle): ()
  local gradient = Instance.new("UIGradient")
  
  if #gradientStyle.Colors == 2 then
    local keypoints = {
      ColorSequenceKeypoint.new(0, gradientStyle.Colors[1]),
      ColorSequenceKeypoint.new(1, gradientStyle.Colors[2]),
    }
    gradient.Color = ColorSequence.new(keypoints)
  else
    local keypoints = {}
    for i, color in ipairs(gradientStyle.Colors) do
      local position = (i - 1) / (#gradientStyle.Colors - 1)
      table.insert(keypoints, ColorSequenceKeypoint.new(position, color))
    end
    gradient.Color = ColorSequence.new(keypoints)
  end
  
  gradient.Rotation = gradientStyle.Rotation
  gradient.Parent = label
end

-- Update an existing billboard
function BillboardService.UpdateBillboard(player: Player, roleName: string, style: RoleStyle): boolean
  local existingBillboard = activeBillboards[player]
  if not existingBillboard or not existingBillboard.Parent then
    -- Create new billboard if none exists
    return BillboardService.CreateBillboard(player, roleName, style) ~= nil
  end

  -- Clear existing content
  local frame = existingBillboard:FindFirstChild("TagFrame")
  if frame then
    frame:Destroy()
  end

  -- Repopulate with new content
  local success = BillboardService.PopulateBillboard(existingBillboard, player, roleName, style)
  if success then
    debugLog(string.format("Updated billboard for %s with role %s", player.Name, roleName))
  end

  return success
end

-- Remove a player's billboard
function BillboardService.RemoveBillboard(player: Player): ()
  local billboard = activeBillboards[player]
  if billboard then
    billboard:Destroy()
    activeBillboards[player] = nil
    debugLog(string.format("Removed billboard for %s", player.Name))
  end
end

-- Get a player's current billboard
function BillboardService.GetBillboard(player: Player): BillboardGui?
  return activeBillboards[player]
end

-- Check if a player has a billboard
function BillboardService.HasBillboard(player: Player): boolean
  local billboard = activeBillboards[player]
  return billboard ~= nil and billboard.Parent ~= nil
end

-- Update billboard settings for all active billboards
function BillboardService.UpdateBillboardSettings(newSettings: BillboardSettings): ()
  billboardSettings = newSettings
  
  -- Update all existing billboards
  for player, billboard in pairs(activeBillboards) do
    if billboard and billboard.Parent then
      billboard.Size = billboardSettings.Size
      billboard.StudsOffset = billboardSettings.StudsOffset
      billboard.AlwaysOnTop = billboardSettings.AlwaysOnTop
      billboard.MaxDistance = billboardSettings.MaxDistance
      billboard.LightInfluence = billboardSettings.LightInfluence
    end
  end
  
  debugLog("Updated billboard settings for all active billboards")
end

-- Get billboard statistics
function BillboardService.GetBillboardStatistics(): {[string]: any}
  local stats = {
    activeBillboards = 0,
    validBillboards = 0,
    invalidBillboards = 0,
  }
  
  for player, billboard in pairs(activeBillboards) do
    stats.activeBillboards = stats.activeBillboards + 1
    
    if billboard and billboard.Parent then
      stats.validBillboards = stats.validBillboards + 1
    else
      stats.invalidBillboards = stats.invalidBillboards + 1
    end
  end
  
  return stats
end

-- Clean up invalid billboards
function BillboardService.CleanupInvalidBillboards(): number
  local removedCount = 0
  
  for player, billboard in pairs(activeBillboards) do
    if not billboard or not billboard.Parent or not player.Parent then
      activeBillboards[player] = nil
      removedCount = removedCount + 1
    end
  end
  
  if removedCount > 0 then
    debugLog(string.format("Cleaned up %d invalid billboards", removedCount))
  end
  
  return removedCount
end

-- Get all active billboards
function BillboardService.GetActiveBillboards(): {[Player]: BillboardGui}
  return activeBillboards
end

-- Get service status
function BillboardService.GetServiceStatus(): {[string]: any}
  local stats = BillboardService.GetBillboardStatistics()
  
  return {
    initialized = isInitialized,
    activeBillboards = stats.activeBillboards,
    validBillboards = stats.validBillboards,
    invalidBillboards = stats.invalidBillboards,
    settings = billboardSettings,
  }
end

-- Cleanup service
function BillboardService.Cleanup(): ()
  -- Remove all billboards
  for player in pairs(activeBillboards) do
    BillboardService.RemoveBillboard(player)
  end
  
  activeBillboards = {}
  isInitialized = false
  debugLog("Billboard service cleaned up")
end

return BillboardService

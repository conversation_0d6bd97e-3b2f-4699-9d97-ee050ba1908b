--!strict
--[[
    TBRDS Service Manager

    Central service coordination and management for TBRDS

    ARCHITECTURE ROLE:
    - Coordinates initialization and lifecycle of all TBRDS services
    - Provides centralized service access and dependency management
    - Handles service health monitoring and error recovery
    - Manages service communication and event coordination

    RESPONSIBILITIES:
    - Initialize all services in correct order
    - Provide service registry and access
    - Monitor service health and performance
    - Handle service failures and recovery
    - Coordinate service shutdown and cleanup

    *Dynamic Innovative Studio*
]]

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")

-- Import services
local ConfigurationService = require(script.Parent.ConfigurationService)
local RoleService = require(script.Parent.RoleService)
local BillboardService = require(script.Parent.BillboardService)
local TagService = require(script.Parent.TagService)

-- Import shared modules
local PerformanceMonitor = require(ReplicatedStorage.TBRDS.Shared.PerformanceMonitor)
local EventSystem = require(ReplicatedStorage.TBRDS.Shared.EventSystem)

local ServiceManager = {}

-- Service registry
local services = {
  Configuration = ConfigurationService,
  Role = RoleService,
  Billboard = BillboardService,
  Tag = TagService,
}

-- Service state
local isInitialized = false
local initializationOrder = {"Configuration", "Role", "Billboard", "Tag"}
local serviceStatus: {[string]: {initialized: boolean, healthy: boolean, lastCheck: number}} = {}
local healthCheckInterval = 30 -- seconds

-- Debug logging
local function debugLog(message: string)
  if ConfigurationService.IsDebugMode() then
    print(string.format("[TBRDS:ServiceManager]: %s", message))
  end
end

-- Initialize all services
function ServiceManager.Initialize(): boolean
  if isInitialized then
    debugLog("Service manager already initialized")
    return true
  end

  debugLog("Initializing TBRDS Service Manager...")

  -- Initialize services in dependency order
  for _, serviceName in ipairs(initializationOrder) do
    local service = services[serviceName]
    if not service then
      warn(string.format("TBRDS: Service '%s' not found", serviceName))
      return false
    end

    debugLog(string.format("Initializing %s service...", serviceName))
    
    local success = service.Initialize()
    if not success then
      warn(string.format("TBRDS: Failed to initialize %s service", serviceName))
      return false
    end

    -- Update service status
    serviceStatus[serviceName] = {
      initialized = true,
      healthy = true,
      lastCheck = os.time(),
    }

    debugLog(string.format("%s service initialized successfully", serviceName))
  end

  -- Start health monitoring
  ServiceManager.StartHealthMonitoring()

  -- Set up periodic validation
  ServiceManager.StartPeriodicValidation()

  isInitialized = true
  debugLog("TBRDS Service Manager initialized successfully")
  
  return true
end

-- Get a specific service
function ServiceManager.GetService(serviceName: string)
  local service = services[serviceName]
  if not service then
    warn(string.format("TBRDS: Service '%s' not found", serviceName))
    return nil
  end

  local status = serviceStatus[serviceName]
  if not status or not status.initialized then
    warn(string.format("TBRDS: Service '%s' not initialized", serviceName))
    return nil
  end

  return service
end

-- Get all services
function ServiceManager.GetAllServices()
  return services
end

-- Check if all services are initialized
function ServiceManager.AreAllServicesInitialized(): boolean
  for serviceName in pairs(services) do
    local status = serviceStatus[serviceName]
    if not status or not status.initialized then
      return false
    end
  end
  return true
end

-- Check if all services are healthy
function ServiceManager.AreAllServicesHealthy(): boolean
  for serviceName in pairs(services) do
    local status = serviceStatus[serviceName]
    if not status or not status.healthy then
      return false
    end
  end
  return true
end

-- Start health monitoring
function ServiceManager.StartHealthMonitoring(): ()
  if not RunService:IsServer() then
    return
  end

  task.spawn(function()
    while isInitialized do
      task.wait(healthCheckInterval)
      ServiceManager.PerformHealthCheck()
    end
  end)

  debugLog("Health monitoring started")
end

-- Perform health check on all services
function ServiceManager.PerformHealthCheck(): ()
  for serviceName, service in pairs(services) do
    local status = serviceStatus[serviceName]
    if status and status.initialized then
      local isHealthy = ServiceManager.CheckServiceHealth(serviceName, service)
      status.healthy = isHealthy
      status.lastCheck = os.time()

      if not isHealthy then
        warn(string.format("TBRDS: Service '%s' health check failed", serviceName))
        PerformanceMonitor.RecordError("SERVICE_HEALTH_CHECK_FAILED")
      end
    end
  end
end

-- Check health of a specific service
function ServiceManager.CheckServiceHealth(serviceName: string, service): boolean
  local success, result = pcall(function()
    -- Check if service has a GetServiceStatus method
    if service.GetServiceStatus then
      local status = service.GetServiceStatus()
      return status and status.initialized == true
    end
    
    -- Basic check - service exists and is callable
    return service ~= nil
  end)

  return success and result
end

-- Start periodic validation
function ServiceManager.StartPeriodicValidation(): ()
  if not RunService:IsServer() then
    return
  end

  task.spawn(function()
    while isInitialized do
      task.wait(60) -- Every minute
      
      -- Validate all tags
      local tagService = ServiceManager.GetService("Tag")
      if tagService then
        local players = game.Players:GetPlayers()
        for _, player in ipairs(players) do
          if player.Parent then
            tagService.RefreshPlayerTag(player)
          end
        end
      end
    end
  end)

  debugLog("Periodic validation started")
end

-- Get service status summary
function ServiceManager.GetServiceStatusSummary(): {[string]: any}
  local summary = {
    managerInitialized = isInitialized,
    totalServices = 0,
    initializedServices = 0,
    healthyServices = 0,
    services = {},
  }

  for serviceName, service in pairs(services) do
    summary.totalServices = summary.totalServices + 1
    
    local status = serviceStatus[serviceName]
    local serviceInfo = {
      initialized = status and status.initialized or false,
      healthy = status and status.healthy or false,
      lastCheck = status and status.lastCheck or 0,
    }

    -- Get detailed status if available
    if service.GetServiceStatus then
      local success, detailedStatus = pcall(service.GetServiceStatus)
      if success then
        serviceInfo.details = detailedStatus
      end
    end

    summary.services[serviceName] = serviceInfo

    if serviceInfo.initialized then
      summary.initializedServices = summary.initializedServices + 1
    end
    
    if serviceInfo.healthy then
      summary.healthyServices = summary.healthyServices + 1
    end
  end

  return summary
end

-- Get system health report
function ServiceManager.GetSystemHealthReport(): string
  local summary = ServiceManager.GetServiceStatusSummary()
  local report = {
    "=== TBRDS System Health Report ===",
    string.format("Manager Initialized: %s", summary.managerInitialized and "✅" or "❌"),
    string.format("Services: %d/%d initialized, %d/%d healthy", 
      summary.initializedServices, summary.totalServices,
      summary.healthyServices, summary.totalServices),
    "",
    "=== Service Details ===",
  }

  for serviceName, serviceInfo in pairs(summary.services) do
    local statusIcon = serviceInfo.healthy and "✅" or "❌"
    local initIcon = serviceInfo.initialized and "✅" or "❌"
    
    table.insert(report, string.format("%s %s Service - Init: %s, Healthy: %s", 
      statusIcon, serviceName, initIcon, serviceInfo.healthy and "Yes" or "No"))
    
    if serviceInfo.details then
      for key, value in pairs(serviceInfo.details) do
        if type(value) ~= "table" then
          table.insert(report, string.format("  %s: %s", key, tostring(value)))
        end
      end
    end
  end

  -- Add performance metrics
  table.insert(report, "")
  table.insert(report, "=== Performance Metrics ===")
  local metrics = PerformanceMonitor.GetMetrics()
  table.insert(report, string.format("Tag Assignments: %d", metrics.TagAssignments))
  table.insert(report, string.format("Security Events: %d", metrics.SecurityEvents))
  table.insert(report, string.format("Cache Hits: %d", metrics.CacheHits))
  table.insert(report, string.format("Cache Misses: %d", metrics.CacheMisses))
  table.insert(report, string.format("Errors: %d", metrics.ErrorCount))

  return table.concat(report, "\n")
end

-- Restart a specific service
function ServiceManager.RestartService(serviceName: string): boolean
  local service = services[serviceName]
  if not service then
    warn(string.format("TBRDS: Service '%s' not found", serviceName))
    return false
  end

  debugLog(string.format("Restarting %s service...", serviceName))

  -- Cleanup if available
  if service.Cleanup then
    local success, err = pcall(service.Cleanup)
    if not success then
      warn(string.format("TBRDS: Error during %s service cleanup: %s", serviceName, tostring(err)))
    end
  end

  -- Reinitialize
  local success = service.Initialize()
  if success then
    serviceStatus[serviceName] = {
      initialized = true,
      healthy = true,
      lastCheck = os.time(),
    }
    debugLog(string.format("%s service restarted successfully", serviceName))
  else
    warn(string.format("TBRDS: Failed to restart %s service", serviceName))
    serviceStatus[serviceName] = {
      initialized = false,
      healthy = false,
      lastCheck = os.time(),
    }
  end

  return success
end

-- Shutdown all services
function ServiceManager.Shutdown(): ()
  debugLog("Shutting down TBRDS Service Manager...")

  -- Cleanup services in reverse order
  local reverseOrder = {}
  for i = #initializationOrder, 1, -1 do
    table.insert(reverseOrder, initializationOrder[i])
  end

  for _, serviceName in ipairs(reverseOrder) do
    local service = services[serviceName]
    if service and service.Cleanup then
      debugLog(string.format("Cleaning up %s service...", serviceName))
      local success, err = pcall(service.Cleanup)
      if not success then
        warn(string.format("TBRDS: Error during %s service cleanup: %s", serviceName, tostring(err)))
      end
    end
    
    serviceStatus[serviceName] = {
      initialized = false,
      healthy = false,
      lastCheck = os.time(),
    }
  end

  isInitialized = false
  debugLog("TBRDS Service Manager shutdown complete")
end

-- Emergency recovery
function ServiceManager.EmergencyRecovery(): boolean
  warn("TBRDS: Performing emergency recovery...")
  
  -- Try to restart all services
  local recoveredServices = 0
  for _, serviceName in ipairs(initializationOrder) do
    if ServiceManager.RestartService(serviceName) then
      recoveredServices = recoveredServices + 1
    end
  end

  local success = recoveredServices == #initializationOrder
  if success then
    debugLog("Emergency recovery completed successfully")
  else
    warn(string.format("TBRDS: Emergency recovery partially failed - %d/%d services recovered", 
      recoveredServices, #initializationOrder))
  end

  return success
end

return ServiceManager

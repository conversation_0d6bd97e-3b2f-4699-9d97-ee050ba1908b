--!strict
--[[
    TBRDS Configuration Service

    Centralized configuration management service for TBRDS

    ARCHITECTURE ROLE:
    - Manages all system configuration and settings
    - Provides runtime configuration updates
    - Handles environment-specific configurations
    - Validates configuration integrity

    RESPONSIBILITIES:
    - Load and validate configuration
    - Provide configuration access to other services
    - Handle configuration updates and notifications
    - Manage feature flags and toggles

    *Dynamic Innovative Studio*
]]

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")

-- Import modules
local TBRDSConfig = require(ReplicatedStorage.Configurations.Systems.TBRDS_Configuration)
local Types = require(ReplicatedStorage.TBRDS.Shared.Types)
local EventSystem = require(ReplicatedStorage.TBRDS.Shared.EventSystem)

type TBRDSConfiguration = Types.TBRDSConfiguration

local ConfigurationService = {}

-- Service state
local isInitialized = false
local currentConfig: TBRDSConfiguration = TBRDSConfig
local configurationListeners: {[string]: (TBRDSConfiguration) -> ()} = {}
local listenerCounter = 0

-- Debug logging
local function debugLog(message: string)
  if ConfigurationService.IsDebugMode() then
    print(string.format("[TBRDS:ConfigurationService]: %s", message))
  end
end

-- Initialize the configuration service
function ConfigurationService.Initialize(): boolean
  if isInitialized then
    debugLog("Configuration service already initialized")
    return true
  end

  debugLog("Initializing configuration service...")

  -- Validate configuration
  local isValid, errors = ConfigurationService.ValidateConfiguration(currentConfig)
  if not isValid then
    warn("TBRDS: Configuration validation failed:")
    for _, error in ipairs(errors) do
      warn("  " .. error)
    end
    return false
  end

  isInitialized = true
  debugLog("Configuration service initialized successfully")
  return true
end

-- Get the current configuration
function ConfigurationService.GetConfiguration(): TBRDSConfiguration
  return currentConfig
end

-- Get a specific configuration section
function ConfigurationService.GetSettings()
  return currentConfig.Settings
end

function ConfigurationService.GetGroups()
  return currentConfig.Groups
end

function ConfigurationService.GetRolePriority(): {string}
  return currentConfig.RolePriority
end

function ConfigurationService.GetGamePasses()
  return currentConfig.GamePasses
end

function ConfigurationService.GetEvents()
  return currentConfig.Events
end

function ConfigurationService.GetRemotes()
  return currentConfig.Remotes
end

function ConfigurationService.GetErrorCodes()
  return currentConfig.ErrorCodes
end

-- Check if debug mode is enabled
function ConfigurationService.IsDebugMode(): boolean
  return currentConfig.Settings.DebugMode
end

-- Check if performance metrics are enabled
function ConfigurationService.IsPerformanceMetricsEnabled(): boolean
  return currentConfig.Settings.EnablePerformanceMetrics
end

-- Check if event system is enabled
function ConfigurationService.IsEventSystemEnabled(): boolean
  return currentConfig.Settings.EnableEventSystem
end

-- Get rate limiting configuration
function ConfigurationService.GetRateLimitConfig()
  return currentConfig.Settings.RateLimit
end

-- Get billboard settings
function ConfigurationService.GetBillboardSettings()
  return currentConfig.Settings.BillboardSettings
end

-- Get anti-exploit settings
function ConfigurationService.GetAntiExploitSettings()
  return currentConfig.Settings.AntiExploit
end

-- Update configuration (server-side only)
function ConfigurationService.UpdateConfiguration(newConfig: TBRDSConfiguration): boolean
  if not RunService:IsServer() then
    warn("TBRDS: Configuration updates can only be performed on the server")
    return false
  end

  -- Validate new configuration
  local isValid, errors = ConfigurationService.ValidateConfiguration(newConfig)
  if not isValid then
    warn("TBRDS: New configuration validation failed:")
    for _, error in ipairs(errors) do
      warn("  " .. error)
    end
    return false
  end

  local oldConfig = currentConfig
  currentConfig = newConfig

  -- Notify listeners
  for listenerId, callback in pairs(configurationListeners) do
    local success, err = pcall(callback, currentConfig)
    if not success then
      warn(string.format("TBRDS: Error in configuration listener '%s': %s", listenerId, tostring(err)))
    end
  end

  -- Fire configuration changed event
  if ConfigurationService.IsEventSystemEnabled() then
    local eventData = EventSystem.CreateEventData(
      game.Players.LocalPlayer or game.Players:GetPlayers()[1], -- Fallback for server
      "ConfigurationChanged",
      "ConfigurationUpdated",
      "ConfigurationService.UpdateConfiguration",
      {
        oldConfig = oldConfig,
        newConfig = newConfig,
      }
    )
    EventSystem.Fire("ConfigurationChanged", eventData)
  end

  debugLog("Configuration updated successfully")
  return true
end

-- Subscribe to configuration changes
function ConfigurationService.SubscribeToChanges(callback: (TBRDSConfiguration) -> ()): string
  listenerCounter = listenerCounter + 1
  local listenerId = string.format("config_listener_%d", listenerCounter)
  configurationListeners[listenerId] = callback
  debugLog(string.format("Configuration listener '%s' registered", listenerId))
  return listenerId
end

-- Unsubscribe from configuration changes
function ConfigurationService.UnsubscribeFromChanges(listenerId: string): boolean
  if configurationListeners[listenerId] then
    configurationListeners[listenerId] = nil
    debugLog(string.format("Configuration listener '%s' unregistered", listenerId))
    return true
  end
  return false
end

-- Validate configuration integrity
function ConfigurationService.ValidateConfiguration(config: TBRDSConfiguration): (boolean, {string})
  local errors = {}

  -- Validate settings
  if not config.Settings then
    table.insert(errors, "Missing Settings section")
  else
    if type(config.Settings.DebugMode) ~= "boolean" then
      table.insert(errors, "Settings.DebugMode must be a boolean")
    end
    
    if type(config.Settings.EnablePerformanceMetrics) ~= "boolean" then
      table.insert(errors, "Settings.EnablePerformanceMetrics must be a boolean")
    end
    
    if not config.Settings.RateLimit then
      table.insert(errors, "Missing Settings.RateLimit section")
    else
      if type(config.Settings.RateLimit.Window) ~= "number" or config.Settings.RateLimit.Window <= 0 then
        table.insert(errors, "Settings.RateLimit.Window must be a positive number")
      end
      
      if type(config.Settings.RateLimit.MaxRequests) ~= "number" or config.Settings.RateLimit.MaxRequests <= 0 then
        table.insert(errors, "Settings.RateLimit.MaxRequests must be a positive number")
      end
    end
  end

  -- Validate groups
  if not config.Groups or not config.Groups.Primary then
    table.insert(errors, "Missing Groups.Primary section")
  else
    if type(config.Groups.Primary.Id) ~= "number" or config.Groups.Primary.Id <= 0 then
      table.insert(errors, "Groups.Primary.Id must be a positive number")
    end
    
    if type(config.Groups.Primary.Name) ~= "string" or config.Groups.Primary.Name == "" then
      table.insert(errors, "Groups.Primary.Name must be a non-empty string")
    end
  end

  -- Validate role priority
  if not config.RolePriority or type(config.RolePriority) ~= "table" or #config.RolePriority == 0 then
    table.insert(errors, "RolePriority must be a non-empty array")
  end

  -- Validate remotes
  if not config.Remotes then
    table.insert(errors, "Missing Remotes section")
  else
    local requiredRemotes = {"TagUpdate", "TagRequest", "TagValidation", "SecurityReport"}
    for _, remoteName in ipairs(requiredRemotes) do
      if not config.Remotes[remoteName] or type(config.Remotes[remoteName]) ~= "string" then
        table.insert(errors, string.format("Missing or invalid Remotes.%s", remoteName))
      end
    end
  end

  return #errors == 0, errors
end

-- Get configuration summary for debugging
function ConfigurationService.GetConfigurationSummary(): {[string]: any}
  return {
    initialized = isInitialized,
    debugMode = ConfigurationService.IsDebugMode(),
    performanceMetrics = ConfigurationService.IsPerformanceMetricsEnabled(),
    eventSystem = ConfigurationService.IsEventSystemEnabled(),
    rateLimitWindow = currentConfig.Settings.RateLimit.Window,
    rateLimitMaxRequests = currentConfig.Settings.RateLimit.MaxRequests,
    primaryGroupId = currentConfig.Groups.Primary.Id,
    roleCount = #currentConfig.RolePriority,
    listenerCount = (function()
      local count = 0
      for _ in pairs(configurationListeners) do
        count = count + 1
      end
      return count
    end)(),
  }
end

-- Reload configuration from source (server-side only)
function ConfigurationService.ReloadConfiguration(): boolean
  if not RunService:IsServer() then
    warn("TBRDS: Configuration reload can only be performed on the server")
    return false
  end

  debugLog("Reloading configuration from source...")

  -- Reload the configuration module
  local success, newConfig = pcall(function()
    -- Force reload by clearing the module cache (if needed)
    return require(ReplicatedStorage.Configurations.Systems.TBRDS_Configuration)
  end)

  if not success then
    warn("TBRDS: Failed to reload configuration: " .. tostring(newConfig))
    return false
  end

  return ConfigurationService.UpdateConfiguration(newConfig)
end

-- Cleanup service
function ConfigurationService.Cleanup(): ()
  configurationListeners = {}
  isInitialized = false
  debugLog("Configuration service cleaned up")
end

return ConfigurationService

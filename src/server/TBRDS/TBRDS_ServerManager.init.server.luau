--!strict
--[[
    TBRDS Server Manager

    Main server initialization script for the Tag-Based Role Display System (TBRDS)
    using Service-Oriented Architecture (SOA)

    ARCHITECTURE ROLE:
    - Initializes the TBRDS system using service-oriented architecture
    - Coordinates service startup and dependency management
    - Provides system health monitoring and debugging capabilities
    - Handles graceful shutdown and error recovery

    SERVICES MANAGED:
    - ConfigurationService: Settings and configuration management
    - RoleService: Role determination and validation
    - BillboardService: Billboard creation and management
    - TagService: Core tag assignment and coordination

    *Dynamic Innovative Studio*
]]

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local Players = game:GetService("Players")
local RunService = game:GetService("RunService")

-- Import the service manager
local ServiceManager = require(script.Parent.Services.ServiceManager)

-- Import shared modules for initialization
local TBRDSRemotes = require(ReplicatedStorage.TBRDS.Remotes)
local EventSystem = require(ReplicatedStorage.TBRDS.Shared.EventSystem)
local PerformanceMonitor = require(ReplicatedStorage.TBRDS.Shared.PerformanceMonitor)

-- System state
local isSystemRunning = false
local startupTime = 0

-- Debug logging
local function debugLog(message: string)
  print(string.format("[TBRDS:ServerManager]: %s", message))
end

-- Initialize the TBRDS system
local function initializeSystem(): boolean
  debugLog("Starting TBRDS system initialization...")
  startupTime = tick()

  -- Initialize remote events first
  if not TBRDSRemotes.InitializeRemotes() then
    warn("TBRDS: Failed to initialize remote events")
    return false
  end

  -- Initialize the service manager and all services
  if not ServiceManager.Initialize() then
    warn("TBRDS: Failed to initialize service manager")
    return false
  end

  -- Verify all services are healthy
  if not ServiceManager.AreAllServicesHealthy() then
    warn("TBRDS: Not all services are healthy after initialization")
    return false
  end

  local initTime = (tick() - startupTime) * 1000
  debugLog(string.format("TBRDS system initialized successfully in %.2fms", initTime))
  
  return true
end

-- Set up system monitoring
local function setupSystemMonitoring(): ()
  -- Health check command for administrators
  Players.PlayerAdded:Connect(function(player)
    player.Chatted:Connect(function(message)
      local configService = ServiceManager.GetService("Configuration")
      if not configService then
        return
      end

      -- Check if player has admin permissions (rank 252 or above)
      local groupId = configService.GetGroups().Primary.Id
      if player:GetRankInGroup(groupId) >= 252 then
        local command = string.lower(message)
        
        if command == "/tbrds status" then
          local report = ServiceManager.GetSystemHealthReport()
          print("=== TBRDS Status Report ===")
          print(report)
          
        elseif command == "/tbrds restart" then
          debugLog(string.format("System restart requested by %s", player.Name))
          if ServiceManager.EmergencyRecovery() then
            debugLog("System restart completed successfully")
          else
            warn("System restart failed")
          end
          
        elseif command == "/tbrds metrics" then
          local metrics = PerformanceMonitor.GetDetailedMetrics()
          print("=== TBRDS Performance Metrics ===")
          print(string.format("Uptime: %d seconds", metrics.uptime))
          print(string.format("Tag Assignments: %d", metrics.basic.TagAssignments))
          print(string.format("Cache Hit Rate: %.2f%%", metrics.cacheHitRate * 100))
          print(string.format("Average Validation Time: %.2fms", metrics.averageValidationTime))
          print(string.format("Error Rate: %.4f errors/second", metrics.errorRate))
          
        elseif command == "/tbrds refresh" then
          debugLog(string.format("Tag refresh requested by %s", player.Name))
          local tagService = ServiceManager.GetService("Tag")
          if tagService then
            local refreshedCount = 0
            for _, targetPlayer in ipairs(Players:GetPlayers()) do
              if targetPlayer.Parent then
                tagService.RefreshPlayerTag(targetPlayer)
                refreshedCount = refreshedCount + 1
              end
            end
            debugLog(string.format("Refreshed tags for %d players", refreshedCount))
          end
        end
      end
    end)
  end)

  -- Handle existing players
  for _, player in ipairs(Players:GetPlayers()) do
    if player.Parent then
      player.Chatted:Connect(function(message)
        -- Same logic as above for existing players
        local configService = ServiceManager.GetService("Configuration")
        if not configService then
          return
        end

        local groupId = configService.GetGroups().Primary.Id
        if player:GetRankInGroup(groupId) >= 252 then
          local command = string.lower(message)
          
          if command == "/tbrds status" then
            local report = ServiceManager.GetSystemHealthReport()
            print("=== TBRDS Status Report ===")
            print(report)
          end
        end
      end)
    end
  end

  debugLog("System monitoring enabled")
end

-- Set up error handling and recovery
local function setupErrorHandling(): ()
  -- Monitor for service failures
  task.spawn(function()
    while isSystemRunning do
      task.wait(60) -- Check every minute
      
      if not ServiceManager.AreAllServicesHealthy() then
        warn("TBRDS: Service health check failed, attempting recovery...")
        
        if ServiceManager.EmergencyRecovery() then
          debugLog("Automatic recovery successful")
        else
          warn("TBRDS: Automatic recovery failed - manual intervention required")
        end
      end
    end
  end)

  -- Set up graceful shutdown
  game:BindToClose(function()
    debugLog("Game shutting down, cleaning up TBRDS...")
    isSystemRunning = false
    ServiceManager.Shutdown()
  end)

  debugLog("Error handling and recovery systems enabled")
end

-- Set up integration with other systems
local function setupSystemIntegration(): ()
  local configService = ServiceManager.GetService("Configuration")
  if not configService or not configService.GetConfiguration().Integration.BroadcastToOtherSystems then
    return
  end

  -- Subscribe to tag change events for system integration
  EventSystem.Subscribe("TagChanged", function(eventData)
    debugLog(string.format("Tag changed: %s -> %s for %s", 
      eventData.OldRole or "None", 
      eventData.NewRole, 
      eventData.Player.Name))
    
    -- Here you could notify other systems like MAFS, MCS, CGS
    -- Example: MAFS could adjust footstep sounds based on role
    -- Example: MCS could update command permissions
    -- Example: CGS could update group-related features
  end)

  EventSystem.Subscribe("SecurityViolation", function(eventData)
    warn(string.format("TBRDS Security Violation: %s for %s", 
      eventData.NewRole, 
      eventData.Player.Name))
    
    -- Could integrate with anti-cheat systems or logging
  end)

  debugLog("System integration enabled")
end

-- Main initialization
debugLog("TBRDS Server Manager starting...")

-- Initialize the system
local success = initializeSystem()
if not success then
  error("TBRDS: Failed to initialize system - aborting startup")
end

-- Set up monitoring and error handling
setupSystemMonitoring()
setupErrorHandling()
setupSystemIntegration()

-- Mark system as running
isSystemRunning = true

-- Final status report
local finalReport = ServiceManager.GetSystemHealthReport()
debugLog("=== TBRDS Startup Complete ===")
print(finalReport)

-- Export system interface for external access
local TBRDSSystem = {
  ServiceManager = ServiceManager,
  
  -- Convenience methods for external systems
  GetPlayerRole = function(player: Player): string
    local tagService = ServiceManager.GetService("Tag")
    if tagService then
      local tagData = tagService.GetPlayerTagData(player)
      return tagData and tagData.Role or "User"
    end
    return "User"
  end,
  
  RefreshPlayerTag = function(player: Player): boolean
    local tagService = ServiceManager.GetService("Tag")
    if tagService then
      local result = tagService.RefreshPlayerTag(player)
      return result.Success
    end
    return false
  end,
  
  GetSystemHealth = function(): string
    return ServiceManager.GetSystemHealthReport()
  end,
  
  IsSystemHealthy = function(): boolean
    return ServiceManager.AreAllServicesHealthy()
  end,
}

-- Store in ServerStorage for other systems to access
local ServerStorage = game:GetService("ServerStorage")
local TBRDSFolder = ServerStorage:FindFirstChild("TBRDS") or Instance.new("Folder")
TBRDSFolder.Name = "TBRDS"
TBRDSFolder.Parent = ServerStorage

-- Create a ModuleScript to expose the system interface
local systemInterface = Instance.new("ModuleScript")
systemInterface.Name = "TBRDSSystem"
systemInterface.Source = "return " .. game:GetService("HttpService"):JSONEncode({
  note = "TBRDS System Interface - Use require(ServerStorage.TBRDS.TBRDSSystem) to access"
})
systemInterface.Parent = TBRDSFolder

-- Store the actual interface in a way other scripts can access
_G.TBRDSSystem = TBRDSSystem

debugLog("TBRDS Server Manager initialization complete!")

return TBRDSSystem

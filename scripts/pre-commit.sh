#!/bin/bash
# 🔒 Pre-commit Hook for Site-112 Project

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[PRE-COMMIT]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Get list of staged Luau files
get_staged_luau_files() {
    git diff --cached --name-only --diff-filter=ACM | grep '\.luau$' || true
}

# Check if tools are available
check_tools() {
    local missing_tools=()
    
    if ! command -v selene &> /dev/null; then
        missing_tools+=("selene")
    fi
    
    if ! command -v stylua &> /dev/null; then
        missing_tools+=("stylua")
    fi
    
    if [ ${#missing_tools[@]} -gt 0 ]; then
        print_error "Missing tools: ${missing_tools[*]}"
        print_error "Please install them with: aftman install"
        return 1
    fi
    
    return 0
}

# Run linting on staged files
lint_staged_files() {
    local staged_files="$1"
    
    if [ -z "$staged_files" ]; then
        print_status "No Luau files staged for commit"
        return 0
    fi
    
    print_status "Linting staged Luau files..."
    
    # Create temporary file list
    local temp_file=$(mktemp)
    echo "$staged_files" > "$temp_file"
    
    # Run selene on staged files
    if echo "$staged_files" | xargs selene; then
        print_success "Linting passed"
    else
        print_error "Linting failed! Please fix the issues above."
        rm "$temp_file"
        return 1
    fi
    
    rm "$temp_file"
    return 0
}

# Check formatting on staged files
check_formatting() {
    local staged_files="$1"
    
    if [ -z "$staged_files" ]; then
        return 0
    fi
    
    print_status "Checking formatting of staged files..."
    
    local formatting_issues=()
    
    # Check each file individually
    while IFS= read -r file; do
        if [ -n "$file" ]; then
            if ! stylua --check "$file" &> /dev/null; then
                formatting_issues+=("$file")
            fi
        fi
    done <<< "$staged_files"
    
    if [ ${#formatting_issues[@]} -gt 0 ]; then
        print_error "Formatting issues found in:"
        for file in "${formatting_issues[@]}"; do
            echo "  - $file"
        done
        print_warning "Run 'stylua <file>' to fix formatting issues"
        return 1
    fi
    
    print_success "All staged files are properly formatted"
    return 0
}

# Check for common issues
check_common_issues() {
    local staged_files="$1"
    
    if [ -z "$staged_files" ]; then
        return 0
    fi
    
    print_status "Checking for common issues..."
    
    local issues_found=false
    
    # Check for debugging statements
    while IFS= read -r file; do
        if [ -n "$file" ] && [ -f "$file" ]; then
            # Check for print statements (might be debugging)
            if grep -n "print(" "$file" | grep -v "-- TODO\|-- FIXME\|-- NOTE" > /dev/null; then
                print_warning "Found print() statements in $file (might be debugging code)"
                grep -n "print(" "$file" | head -3
                issues_found=true
            fi
            
            # Check for TODO/FIXME without proper formatting
            if grep -n "TODO\|FIXME" "$file" | grep -v "-- TODO\|-- FIXME" > /dev/null; then
                print_warning "Found unformatted TODO/FIXME in $file"
                grep -n "TODO\|FIXME" "$file" | grep -v "-- TODO\|-- FIXME" | head -3
            fi
        fi
    done <<< "$staged_files"
    
    if [ "$issues_found" = true ]; then
        print_warning "Common issues detected. Review the warnings above."
        echo "Continue anyway? (y/N)"
        read -r response
        if [[ ! "$response" =~ ^[Yy]$ ]]; then
            return 1
        fi
    fi
    
    return 0
}

# Check commit message format
check_commit_message() {
    # Skip if this is an amend or merge commit
    if [ -f .git/MERGE_HEAD ] || [ -f .git/CHERRY_PICK_HEAD ]; then
        return 0
    fi
    
    # Get the commit message
    local commit_msg_file="$1"
    if [ -z "$commit_msg_file" ] || [ ! -f "$commit_msg_file" ]; then
        return 0
    fi
    
    local commit_msg=$(head -1 "$commit_msg_file")
    
    # Check conventional commit format
    if [[ "$commit_msg" =~ ^(feat|fix|docs|style|refactor|test|chore|perf|ci|build)(\(.+\))?: .+ ]]; then
        print_success "Commit message follows conventional format"
        return 0
    else
        print_warning "Commit message doesn't follow conventional commit format"
        print_warning "Examples:"
        print_warning "  feat: add new MAFS sound system"
        print_warning "  fix: resolve client-server sync issue"
        print_warning "  docs: update API documentation"
        echo "Continue anyway? (y/N)"
        read -r response
        if [[ ! "$response" =~ ^[Yy]$ ]]; then
            return 1
        fi
    fi
    
    return 0
}

# Main pre-commit function
main() {
    print_status "Running pre-commit checks..."
    
    # Check if tools are available
    if ! check_tools; then
        exit 1
    fi
    
    # Get staged Luau files
    local staged_files
    staged_files=$(get_staged_luau_files)
    
    # Run checks
    local exit_code=0
    
    # Lint staged files
    if ! lint_staged_files "$staged_files"; then
        exit_code=1
    fi
    
    # Check formatting
    if ! check_formatting "$staged_files"; then
        exit_code=1
    fi
    
    # Check for common issues
    if ! check_common_issues "$staged_files"; then
        exit_code=1
    fi
    
    # Check commit message if provided
    if [ -n "$1" ]; then
        if ! check_commit_message "$1"; then
            exit_code=1
        fi
    fi
    
    if [ $exit_code -eq 0 ]; then
        print_success "All pre-commit checks passed! 🎉"
    else
        print_error "Pre-commit checks failed!"
        echo ""
        echo "To bypass these checks (not recommended), use:"
        echo "  git commit --no-verify"
    fi
    
    exit $exit_code
}

# Run main function
main "$@"

--!strict
--[[
    TBRDS Integration Example

    This example demonstrates how external systems can integrate with the
    new Service-Oriented Architecture (SOA) of TBRDS.

    This example shows:
    1. How to access the TBRDS system
    2. How to get player roles and tag data
    3. How to subscribe to tag change events
    4. How to integrate with other game systems

    *Dynamic Innovative Studio*
]]

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- Import TBRDS API
local TBRDSAPI = require(ReplicatedStorage.TBRDS.Shared.API)

-- Example: Custom System Integration
local CustomSystem = {}

-- Initialize the custom system with TBRDS integration
function CustomSystem.Initialize()
    print("CustomSystem: Initializing with TBRDS integration...")
    
    -- Wait for TBRDS to be ready
    local attempts = 0
    while not TBRDSAPI.GetSystemHealth().servicesHealthy and attempts < 30 do
        task.wait(1)
        attempts = attempts + 1
    end
    
    if attempts >= 30 then
        warn("CustomSystem: TBRDS not ready after 30 seconds")
        return false
    end
    
    -- Subscribe to tag change events
    local subscriptionId = TBRDSAPI.SubscribeToTagChanges(function(eventData)
        CustomSystem.HandleTagChanged(eventData)
    end)
    
    print("CustomSystem: Subscribed to TBRDS tag changes with ID:", subscriptionId)
    
    -- Set up player management
    Players.PlayerAdded:Connect(CustomSystem.HandlePlayerJoined)
    Players.PlayerRemoving:Connect(CustomSystem.HandlePlayerLeaving)
    
    -- Handle existing players
    for _, player in ipairs(Players:GetPlayers()) do
        CustomSystem.HandlePlayerJoined(player)
    end
    
    print("CustomSystem: Initialization complete")
    return true
end

-- Handle player joining
function CustomSystem.HandlePlayerJoined(player: Player)
    print(string.format("CustomSystem: Player %s joined", player.Name))
    
    -- Get the player's role
    local role = TBRDSAPI.GetPlayerRole(player)
    print(string.format("CustomSystem: Player %s has role: %s", player.Name, role))
    
    -- Apply custom system behavior based on role
    CustomSystem.ApplyRoleBasedBehavior(player, role)
end

-- Handle player leaving
function CustomSystem.HandlePlayerLeaving(player: Player)
    print(string.format("CustomSystem: Player %s left", player.Name))
    -- Clean up any custom system data for this player
end

-- Handle tag changes
function CustomSystem.HandleTagChanged(eventData)
    local player = eventData.Player
    local oldRole = eventData.OldRole
    local newRole = eventData.NewRole
    
    print(string.format("CustomSystem: %s role changed from %s to %s", 
        player.Name, oldRole or "None", newRole))
    
    -- Apply new role-based behavior
    CustomSystem.ApplyRoleBasedBehavior(player, newRole)
    
    -- Example: Special handling for specific role changes
    if newRole == "Developer" and oldRole ~= "Developer" then
        CustomSystem.GrantDeveloperPrivileges(player)
    elseif oldRole == "Developer" and newRole ~= "Developer" then
        CustomSystem.RevokeDeveloperPrivileges(player)
    end
end

-- Apply role-based behavior
function CustomSystem.ApplyRoleBasedBehavior(player: Player, role: string)
    -- Example: Different behaviors based on role
    if role == "BleckWolf25" or role == "Anonmancer" then
        -- Founder/Co-Founder privileges
        CustomSystem.GrantFounderPrivileges(player)
        
    elseif role == "Senior Moderator" or role == "Game Moderator" then
        -- Moderator privileges
        CustomSystem.GrantModeratorPrivileges(player)
        
    elseif role == "Developer" then
        -- Developer privileges
        CustomSystem.GrantDeveloperPrivileges(player)
        
    elseif role == "Supporter" then
        -- Supporter benefits
        CustomSystem.GrantSupporterBenefits(player)
        
    else
        -- Default user behavior
        CustomSystem.ApplyDefaultBehavior(player)
    end
end

-- Example privilege functions
function CustomSystem.GrantFounderPrivileges(player: Player)
    print(string.format("CustomSystem: Granting founder privileges to %s", player.Name))
    -- Example: Give access to all areas, special commands, etc.
end

function CustomSystem.GrantModeratorPrivileges(player: Player)
    print(string.format("CustomSystem: Granting moderator privileges to %s", player.Name))
    -- Example: Give moderation tools, kick/ban commands, etc.
end

function CustomSystem.GrantDeveloperPrivileges(player: Player)
    print(string.format("CustomSystem: Granting developer privileges to %s", player.Name))
    -- Example: Give access to developer tools, debug commands, etc.
end

function CustomSystem.GrantSupporterBenefits(player: Player)
    print(string.format("CustomSystem: Granting supporter benefits to %s", player.Name))
    -- Example: Give cosmetic items, special effects, etc.
end

function CustomSystem.RevokeDeveloperPrivileges(player: Player)
    print(string.format("CustomSystem: Revoking developer privileges from %s", player.Name))
    -- Example: Remove developer tools access
end

function CustomSystem.ApplyDefaultBehavior(player: Player)
    print(string.format("CustomSystem: Applying default behavior to %s", player.Name))
    -- Example: Standard user permissions and features
end

-- Example: MAFS Integration
local MAFSIntegration = {}

function MAFSIntegration.Initialize()
    -- Subscribe to TBRDS tag changes to modify footstep sounds
    TBRDSAPI.SubscribeToTagChanges(function(eventData)
        MAFSIntegration.UpdateFootstepProfile(eventData.Player, eventData.NewRole)
    end)
end

function MAFSIntegration.UpdateFootstepProfile(player: Player, role: string)
    -- Example: Different footstep sounds based on role
    local footstepProfile = "default"
    
    if role == "BleckWolf25" or role == "Anonmancer" then
        footstepProfile = "founder"
    elseif role == "Developer" then
        footstepProfile = "developer"
    elseif string.find(role, "Moderator") then
        footstepProfile = "moderator"
    end
    
    print(string.format("MAFS: Setting footstep profile '%s' for %s", footstepProfile, player.Name))
    -- Here you would call the actual MAFS API to set the profile
end

-- Example: Permission System Integration
local PermissionSystem = {}

function PermissionSystem.Initialize()
    -- Subscribe to TBRDS tag changes to update permissions
    TBRDSAPI.SubscribeToTagChanges(function(eventData)
        PermissionSystem.UpdatePlayerPermissions(eventData.Player, eventData.NewRole)
    end)
end

function PermissionSystem.UpdatePlayerPermissions(player: Player, role: string)
    -- Example: Grant permissions based on role
    local permissions = PermissionSystem.GetRolePermissions(role)
    
    print(string.format("Permissions: Updating permissions for %s with role %s", player.Name, role))
    
    -- Store permissions for the player
    -- This would integrate with your actual permission system
end

function PermissionSystem.GetRolePermissions(role: string): {string}
    local permissionMap = {
        ["BleckWolf25"] = {"*"}, -- All permissions
        ["Anonmancer"] = {"*"}, -- All permissions
        ["Senior Moderator"] = {"kick", "ban", "mute", "teleport", "noclip"},
        ["Game Moderator"] = {"kick", "mute", "teleport"},
        ["Junior Moderator"] = {"kick", "mute"},
        ["Developer"] = {"debug", "spawn", "teleport", "noclip"},
        ["Trusted"] = {"teleport"},
        ["Supporter"] = {"cosmetics"},
        ["User"] = {"basic"},
    }
    
    return permissionMap[role] or {"basic"}
end

-- Example: Analytics Integration
local AnalyticsSystem = {}

function AnalyticsSystem.Initialize()
    -- Track role distribution and changes
    TBRDSAPI.SubscribeToTagChanges(function(eventData)
        AnalyticsSystem.TrackRoleChange(eventData)
    end)
    
    -- Periodic role statistics
    task.spawn(function()
        while true do
            task.wait(300) -- Every 5 minutes
            AnalyticsSystem.RecordRoleStatistics()
        end
    end)
end

function AnalyticsSystem.TrackRoleChange(eventData)
    -- Example: Send role change data to analytics service
    local data = {
        player = eventData.Player.Name,
        userId = eventData.Player.UserId,
        oldRole = eventData.OldRole,
        newRole = eventData.NewRole,
        timestamp = eventData.Timestamp,
        source = eventData.Source,
    }
    
    print("Analytics: Role change tracked:", game:GetService("HttpService"):JSONEncode(data))
end

function AnalyticsSystem.RecordRoleStatistics()
    local stats = TBRDSAPI.GetRoleStatistics()
    print("Analytics: Current role distribution:", game:GetService("HttpService"):JSONEncode(stats))
end

-- Example: Main Integration Script
local function main()
    print("=== TBRDS Integration Example ===")
    
    -- Initialize all integrated systems
    local systems = {
        CustomSystem,
        MAFSIntegration,
        PermissionSystem,
        AnalyticsSystem,
    }
    
    for _, system in ipairs(systems) do
        local success, err = pcall(system.Initialize)
        if not success then
            warn(string.format("Failed to initialize system: %s", tostring(err)))
        end
    end
    
    -- Example: Query TBRDS system health
    task.spawn(function()
        while true do
            task.wait(60) -- Every minute
            local health = TBRDSAPI.GetSystemHealth()
            if not health.servicesHealthy then
                warn("TBRDS services are not healthy!")
            end
        end
    end)
    
    print("=== Integration Example Complete ===")
end

-- Run the example
main()

return {
    CustomSystem = CustomSystem,
    MAFSIntegration = MAFSIntegration,
    PermissionSystem = PermissionSystem,
    AnalyticsSystem = AnalyticsSystem,
}
